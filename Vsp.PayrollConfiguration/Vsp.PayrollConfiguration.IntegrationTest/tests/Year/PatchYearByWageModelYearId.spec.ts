import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./Year.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "wagemodels/years/{yearId}",
    method: "PATCH",
    params: {
        yearId: entityData.yearIds.QA_Year_PATCH_WM.year2025,
    },
    modules: {
        methodNotAllowed: ["DELETE", "GET", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,     // Covered in C# integration tests
        token: "QA_PayrollConfiguration1",
        body: {
            "standardShift": {
                "shiftNumber": 1
            },
            "standardEmployeeProfile": {
                "employeeProfileNumber": 1
            }
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one request to trigger validation
        "PatchYearByWageModelYearId should return 400 and the correct message for standardShift.shiftNumber=-1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_Year_PATCH_WM.year2025,
                },
                body: {
                    "standardShift": {
                        "shiftNumber": -1
                    },
                    "standardEmployeeProfile": {
                        "employeeProfileNumber": 1
                    }
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 1,
                        mode: ValidationMode.count
                    },
                    {
                        key: "messages[0].messageCode",
                        value: "ModelStateValidationError",
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "messages[0].properties",
                        value: "{\"standardShift.ShiftNumber\":\"The field ShiftNumber must be between 1 and 15.\"}",
                        mode: ValidationMode.strict,
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PatchYearByWageModelYearId should return 403 when yearId=non-YearGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: sharedData.nonEntityGuid
                },
                body: {
                    "standardShift": {
                        "shiftNumber": 1
                    },
                    "standardEmployeeProfile": {
                        "employeeProfileNumber": 1
                    }
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "PatchYearByWageModelYearId should return 403 when yearId=collectiveLaborAgreementYearId (blocked level)": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_Year_GET_CLA.year2025
                },
                body: {
                    "standardShift": {
                        "shiftNumber": 1
                    },
                    "standardEmployeeProfile": {
                        "employeeProfileNumber": 1
                    }
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.blockedInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "PatchYearByWageModelYearId should return 403 when yearId=payrollAdministrationYearId (blocked level)": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_Year_GET_PA.year2025
                },
                body: {
                    "standardShift": {
                        "shiftNumber": 1
                    },
                    "standardEmployeeProfile": {
                        "employeeProfileNumber": 1
                    }
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.blockedInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
