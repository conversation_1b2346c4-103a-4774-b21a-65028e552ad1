import {testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./DifferentiatedReturnToWorkFund.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrolladministrations/differentiatedreturntoworkfunds/{differentiatedReturnToWorkFundId}",
    method: "DELETE",
    params: {
        differentiatedReturnToWorkFundId: entityData.differentiatedReturnToWorkFundIds.QA_DifferentiatedReturnToWorkFund_PUT_PA.Year2025,
    },
    modules: {
        methodNotAllowed: ["PATCH", "POST"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "DeleteDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundId should return 403 when differentiatedReturnToWorkFundId=non-differentiatedReturnToWorkFundGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    differentiatedReturnToWorkFundId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonInheritanceGuid,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
