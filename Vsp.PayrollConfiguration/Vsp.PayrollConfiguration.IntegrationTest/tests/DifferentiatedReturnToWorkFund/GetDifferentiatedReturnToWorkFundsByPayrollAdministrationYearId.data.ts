export const expectedResponses = {
    QA_DifferentiatedReturnToWorkFund_GET_PA: {
        year2025: {
            _embedded: [
                {
                    "id": "bf3c804c-e064-453c-99a0-a1cee506ae99",
                    "startPayrollPeriod": {
                        "year": 2025,
                        "periodNumber": 1,
                        "payrollPeriodId": 202501,
                        "periodStartDate": "2025-01-01",
                        "periodEndDate": "2025-01-31"
                    },
                    "wga": {
                        "totalContribution": 2,
                        "employmentContribution": 1
                    },
                    "year": 2025,
                    "zw": {
                        "totalContribution": 3
                    }
                },
                {
                    "id": "452a02d2-ed1b-4807-8d36-cc58ba8cc0dd",
                    "startPayrollPeriod": {
                        "year": 2025,
                        "periodNumber": 2,
                        "payrollPeriodId": 202502,
                        "periodStartDate": "2025-02-01",
                        "periodEndDate": "2025-02-28"
                    },
                    "wga": {
                        "totalContribution": 2.2,
                        "employmentContribution": 1.2
                    },
                    "year": 2025,
                    "zw": {
                        "totalContribution": 3.2
                    }
                },
                {
                    "id": "55d93032-b51d-4dc7-94aa-b20115f8ed01",
                    "startPayrollPeriod": {
                        "year": 2025,
                        "periodNumber": 3,
                        "payrollPeriodId": 202503,
                        "periodStartDate": "2025-03-01",
                        "periodEndDate": "2025-03-31"
                    },
                    "wga": {
                        "totalContribution": 3.32,
                        "employmentContribution": 1.32
                    },
                    "year": 2025,
                    "zw": {
                        "totalContribution": 3.32
                    }
                },
                {
                    "id": "808a618b-cad4-41f4-96f4-27f385f3d969",
                    "startPayrollPeriod": {
                        "year": 2025,
                        "periodNumber": 4,
                        "payrollPeriodId": 202504,
                        "periodStartDate": "2025-04-01",
                        "periodEndDate": "2025-04-30"
                    },
                    "wga": {
                        "totalContribution": 2.432,
                        "employmentContribution": 1.432
                    },
                    "year": 2025,
                    "zw": {
                        "totalContribution": 3.432
                    }
                },
                {
                    "id": "376f66ae-4a7b-4110-b4aa-963efc705567",
                    "startPayrollPeriod": {
                        "year": 2025,
                        "periodNumber": 5,
                        "payrollPeriodId": 202505,
                        "periodStartDate": "2025-05-01",
                        "periodEndDate": "2025-05-31"
                    },
                    "wga": {
                        "totalContribution": 0,
                        "employmentContribution": 0
                    },
                    "year": 2025,
                    "zw": {
                        "totalContribution": 0
                    }
                }
            ]
        }
    }
};
