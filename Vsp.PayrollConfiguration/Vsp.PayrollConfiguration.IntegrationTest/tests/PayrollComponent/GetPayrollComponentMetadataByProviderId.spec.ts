import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "providers/{providerId}/payrollcomponents/metadata",
    method: "GET",
    params: {
        providerId: sharedData.providerIds.QA_PayrollConfiguration1,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        "GetPayrollComponentMetadataByProviderId should return 200, the correct resourceVersion+obsoleteDate, and the correct list of provider metadata (for PayrollComponents) to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    providerId: sharedData.providerIds.QA_PayrollConfiguration1,
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    // check object-names only, array-content = generic and checked at the apiGateway/v2/GetOptionsByField-endpoint
                    {
                        key: "content.balanceSheetSide",
                        mode: ValidationMode.nonEmpty,
                    },
                    {
                        key: "content.baseForCalculationBter",
                        mode: ValidationMode.nonEmpty,
                    },
                    {
                        key: "content.category",
                        mode: ValidationMode.nonEmpty,
                    },
                    {
                        key: "content.column",
                        mode: ValidationMode.nonEmpty,
                    },
                    {
                        key: "content.costsEmployer",
                        mode: ValidationMode.nonEmpty,
                    },
                    {
                        key: "content.deductionOrPayment",
                        mode: ValidationMode.nonEmpty,
                    },
                    {
                        key: "content.hoursIndication",
                        mode: ValidationMode.nonEmpty,
                    },
                    {
                        key: "content.paymentPeriod",
                        mode: ValidationMode.nonEmpty,
                    },
                    {
                        key: "content.socialSecurityLiable",
                        mode: ValidationMode.nonEmpty,
                    },
                    {
                        key: "content.taxLiable",
                        mode: ValidationMode.nonEmpty,
                    },
                ],
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetPayrollComponentMetadataByProviderId should return 403 when providerId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    providerId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonProviderGuid,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});