import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./Shift.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "shifts",
    method: "POST",
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,     // Covered in C# integration tests
        token: "QA_PayrollConfiguration1",
        body: {
            "bonusPercentage": 9.34,
            "fullTimeHoursPerWeek": 8.12,
            "shiftNumber": 11,
            "startPayrollPeriod": {
                "periodNumber": 1
            },
            "year": 2025
        },
        queryStringParams: {
            collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_Shift_GET_CLA,
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---201---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger validation
        "PostShiftByInheritanceLevelId should return 400 and the correct message when shiftNumber<0 ": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {},
                queryStringParams: {
                    collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_Shift_GET_CLA,
                },
                body: {
                    "bonusPercentage": 24.83,
                    "fullTimeHoursPerWeek": 8.12,
                    "shiftNumber": -11,
                    "startPayrollPeriod": {
                        "year": 2025,
                        "periodNumber": 1
                    },
                    "year": 2025
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 1,
                        mode: ValidationMode.count
                    },
                    {
                        key: "messages[0].messageCode",
                        value: "ModelStateValidationError",
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "messages[0].properties",
                        value: "{\"shiftNumber\":\"The field ShiftNumber must be between 1 and 15.\"}",
                        mode: ValidationMode.strict,
                    },
                ],
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PostShiftByInheritanceLevelId should return 403 when payrollAdministrationId=non-payrollAdministrationGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {},
                queryStringParams: {
                    payrollAdministrationId: sharedData.nonEntityGuid,
                },
                body: {
                    "bonusPercentage": 24.83,
                    "fullTimeHoursPerWeek": 8.12,
                    "shiftNumber": 11,
                    "startPayrollPeriod": {
                        "periodNumber": 1
                    },
                    "year": 2025,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonInheritanceGuid,
                        mode: ValidationMode.partial
                    }
                ],
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
