import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./PayrollComponentExtra.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrollcomponents/{payrollComponentId}/extra/metadata",
    method: "GET",
    params: {
        payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponentExtra_GET_PA.year2025.component51,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        "GetPayrollComponentExtraMetadataByPayrollComponentId should return 200, the correct resourceVersion+obsoleteDate, and the correct list of metadata for a route payroll component, to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponentExtra_GET_PA.year2025.component51,
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    // check object-names only, array-content = generic and checked at the apiGateway/v2/GetOptionsByField-endpoint
                    {
                        key: "content.routeType",
                        mode: ValidationMode.nonEmpty,
                    },
                ],
            }
        },
        "GetPayrollComponentExtraMetadataByPayrollComponentId should return 200, and the correct list of metadata for a non-route payroll component, to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponentExtra_GET_PA.year2025.component100,
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    // null signals that the payroll component is NOT suitable for routing
                    {
                        key: "content.routeType",
                        value: null,
                        mode: ValidationMode.strict,
                    },
                ],
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetPayrollComponentExtraMetadataByPayrollComponentId should return 403 when payrollComponentId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});