import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./Year.data";
import * as endpointData from "./GetYearsMinimizedByInheritanceLevelId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "years/minimized",
    method: "GET",
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
        queryStringParams: {
            collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_Year_GET_CLA,
        },
    },
    tokens: {
        QA_AKPayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        "GetYearsMinimizedByInheritanceLevelId(CLA) should return 200, the correct resourceVersion+obsoleteDate and the correct list of minimized Years for collective labor agreement: QA_Year_GET_CLA to provider user QA_AKPayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                queryStringParams: {
                    collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_Year_GET_CLA,
                    filter: "year gt 2020 AND year lt 2026",
                    orderBy: "year",
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: 5,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: 5,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetYearsMinimizedByInheritanceLevelId(WM) should return 200 and the correct list of Years for wage model QA_Year_GET_WM": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                queryStringParams: {
                    wageModelId: entityData.wageModelIds.QA_Year_GET_WM,
                    filter: "year gt 2020 AND year lt 2026",
                    orderBy: "year",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: 5,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: 5,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_WM._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetYearsMinimizedByInheritanceLevelId(PA) should return 200 and the correct list of Years for payroll administration: QA_Year_GET_PA": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                queryStringParams: {
                    payrollAdministrationId: entityData.payrollAdministrationIds.QA_Year_GET_PA,
                    orderBy: "year",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: 6,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: 6,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_PA._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetYearsMinimizedByInheritanceLevelId with filter='year gt 2023 AND year lt 2026' should return 200 and the filtered list of Years": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                params: {
                },
                queryStringParams: {
                    collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_Year_GET_CLA,
                    filter: "year gt 2023 AND year lt 2026",
                    orderBy: "year",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "totalSize",
                        value: 2,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: 2,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded[0].id",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA._embedded[3].id,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "_embedded[1].id",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA._embedded[4].id,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetYearsMinimizedByInheritanceLevelId with orderBy='-year' should return 200 and the ordered list of Years": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                params: {
                },
                queryStringParams: {
                    collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_Year_GET_CLA,
                    filter: "year gt 2023 AND year lt 2026",
                    orderBy: "-year",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "_embedded[0].id",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA._embedded[4].id,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "_embedded[1].id",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA._embedded[3].id,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetYearsMinimizedByInheritanceLevelId with x-reportinput should return 200 and the correct report of Years": {
            request: {
                headers: {
                    "accept": "text/csv",
                    "x-reportinput": '{"FileNameWithoutExtension":"YearsByInheritanceLevel","Fields":[{"FieldName":"inheritanceLevel.type.value","ReportColumnName":"Niveau"},{"FieldName":"payrollPeriodType.value","ReportColumnName":"Verloningstype"},{"FieldName":"year","ReportColumnName":"Jaar"},{"FieldName":"id","ReportColumnName":"LoketID"}]}',
                    "content-type": "application/json",
                },
                token: "QA_AKPayrollConfiguration1",
                queryStringParams: {
                    collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_Year_GET_CLA,
                    orderBy: "year",
                    filter: "year gt 2024 AND year lt 2026",
                },
            },
            response: {
                code: 200,
                headers: {
                    "content-disposition": new RegExp("attachment; filename=\"YearsByInheritanceLevel " + sharedData.sharedCurrentDate + " ([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9].csv\""),
                    "content-type": "text/csv; charset=utf-8",
                },
                validations: [
                    {
                        value: "Niveau;Verloningstype;Jaar;LoketID\r\nCollectiveLaborAgreement;Maand;2025;0000093b-07e9-0000-0000-000000000000\r\n",
                        mode: ValidationMode.strict,
                    },
                ],
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetYearsMinimizedByInheritanceLevelId should return 403 when wageModelId=non-wageModelGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                queryStringParams: {
                    wageModelId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonInheritanceGuid,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
