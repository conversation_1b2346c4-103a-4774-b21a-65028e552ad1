export const expectedResponses = {
    QA_Year_GET_PA: {
        _embedded: [
            {
                "testYear": true,
                "yearTransition": {
                    "isRequested": false,
                    "isPerformed": true,
                    "performedDate": "2025-04-16"
                },
                "dateAvailableEss": "2025-04-16",
                "sendEssMail": false,
                "dateEssMail": null,
                "zwSelfInsurerStartPayrollPeriod": null,
                "aof": null,
                "id": "0000093d-07e4-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "d8706816-6153-455a-934d-4e939a2af6c3",
                    "type": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    }
                },
                "year": 2020,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 15,
                    "fullTimeHoursPerWeek": 16.25,
                    "bonusPercentage": 16.025
                },
                "standardEmployeeProfile": {
                    "employeeProfileNumber": 2,
                    "description": "PA employee profile 2020"
                },
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardEmployeeProfile": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    }
                }
            },
            {
                "testYear": true,
                "yearTransition": {
                    "isRequested": false,
                    "isPerformed": true,
                    "performedDate": "2025-04-16"
                },
                "dateAvailableEss": "2025-04-30",
                "sendEssMail": false,
                "dateEssMail": null,
                "zwSelfInsurerStartPayrollPeriod": {
                    "year": 2021,
                    "periodNumber": 1,
                    "payrollPeriodId": 202101,
                    "periodStartDate": "2021-01-04",
                    "periodEndDate": "2021-01-31"
                },
                "aof": null,
                "id": "0000093d-07e5-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "d8706816-6153-455a-934d-4e939a2af6c3",
                    "type": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    }
                },
                "year": 2021,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 1,
                    "fullTimeHoursPerWeek": 40.21,
                    "bonusPercentage": 0.021
                },
                "standardEmployeeProfile": null,
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardEmployeeProfile": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                }
            },
            {
                "testYear": false,
                "yearTransition": {
                    "isRequested": false,
                    "isPerformed": true,
                    "performedDate": "2025-04-16"
                },
                "dateAvailableEss": "2025-04-16",
                "sendEssMail": true,
                "dateEssMail": "2025-04-16",
                "zwSelfInsurerStartPayrollPeriod": {
                    "year": 2022,
                    "periodNumber": 13,
                    "payrollPeriodId": 202213,
                    "periodStartDate": "2022-12-05",
                    "periodEndDate": "2023-01-01"
                },
                "aof": {
                    "key": 1,
                    "value": "Kleine werkgever"
                },
                "id": "0000093d-07e6-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "d8706816-6153-455a-934d-4e939a2af6c3",
                    "type": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    }
                },
                "year": 2022,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 1,
                    "fullTimeHoursPerWeek": 40.22,
                    "bonusPercentage": 0.022
                },
                "standardEmployeeProfile": null,
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    },
                    "standardEmployeeProfile": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    }
                }
            },
            {
                "testYear": false,
                "yearTransition": {
                    "isRequested": false,
                    "isPerformed": true,
                    "performedDate": "2025-04-17"
                },
                "dateAvailableEss": "2100-01-01",
                "sendEssMail": true,
                "dateEssMail": null,
                "zwSelfInsurerStartPayrollPeriod": null,
                "aof": {
                    "key": 2,
                    "value": "(Middel) grote werkgever"
                },
                "id": "0000093d-07e7-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "d8706816-6153-455a-934d-4e939a2af6c3",
                    "type": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    }
                },
                "year": 2023,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 2,
                    "fullTimeHoursPerWeek": 38.23,
                    "bonusPercentage": 5.023
                },
                "standardEmployeeProfile": {
                    "employeeProfileNumber": 1,
                    "description": "WM employee profile 2023"
                },
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardEmployeeProfile": {
                        "key": 2,
                        "value": "WageModel"
                    }
                }
            },
            {
                "testYear": false,
                "yearTransition": {
                    "isRequested": false,
                    "isPerformed": true,
                    "performedDate": "2025-04-17"
                },
                "dateAvailableEss": "2025-04-17",
                "sendEssMail": false,
                "dateEssMail": null,
                "zwSelfInsurerStartPayrollPeriod": null,
                "aof": {
                    "key": 1,
                    "value": "Kleine werkgever"
                },
                "id": "0000093d-07e8-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "d8706816-6153-455a-934d-4e939a2af6c3",
                    "type": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    }
                },
                "year": 2024,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 99,
                    "fullTimeHoursPerWeek": 0,
                    "bonusPercentage": 0
                },
                "standardEmployeeProfile": {
                    "employeeProfileNumber": 99,
                    "description": null
                },
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    },
                    "standardEmployeeProfile": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    }
                }
            },
            {
                "testYear": false,
                "yearTransition": {
                    "isRequested": false,
                    "isPerformed": false,
                    "performedDate": null
                },
                "dateAvailableEss": null,
                "sendEssMail": false,
                "dateEssMail": null,
                "zwSelfInsurerStartPayrollPeriod": null,
                "aof": {
                    "key": 1,
                    "value": "Kleine werkgever"
                },
                "id": "0000093d-07e9-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "d8706816-6153-455a-934d-4e939a2af6c3",
                    "type": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    }
                },
                "year": 2025,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 12,
                    "fullTimeHoursPerWeek": 36.25,
                    "bonusPercentage": 10.025
                },
                "standardEmployeeProfile": {
                    "employeeProfileNumber": 2,
                    "description": "PA employee profile 2021"
                },
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    },
                    "standardEmployeeProfile": {
                        "key": 3,
                        "value": "PayrollAdministration"
                    }
                }
            }
        ]
    },
};
