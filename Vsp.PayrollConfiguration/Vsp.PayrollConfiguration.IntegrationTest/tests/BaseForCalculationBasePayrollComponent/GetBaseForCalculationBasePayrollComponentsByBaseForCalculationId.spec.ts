import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculationBasePayrollComponent.data";
import * as endpointData from "./GetBaseForCalculationBasePayrollComponentsByBaseForCalculationId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "basesforcalculation/{baseForCalculationId}/basepayrollcomponents",
    method: "GET",
    params: {
        baseForCalculationId: entityData.baseForCalculationIds.QA_BFC_BasePayrollComponent_GET_CLA_2025.year2025.baseForCalculation_1,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        "GetBaseForCalculationBasePayrollComponentsByBaseForCalculationId should return 200, the correct list of the base payroll components to the provided base for calculation, for 2025 at collective labor agreement level to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: entityData.baseForCalculationIds.QA_BFC_BasePayrollComponent_GET_CLA_2025.year2025.baseForCalculation_1,
                },
                queryStringParams: {
                    orderBy: "payrollComponent.key",
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_BFC_BasePayrollComponent_GET_CLA.year2025.baseForCalculation_1._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_BFC_BasePayrollComponent_GET_CLA.year2025.baseForCalculation_1._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_BFC_BasePayrollComponent_GET_CLA.year2025.baseForCalculation_1._embedded,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        // ---403---
        "GetBaseForCalculationBasePayrollComponentsByBaseForCalculationId should return 403 when baseForCalculationId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});