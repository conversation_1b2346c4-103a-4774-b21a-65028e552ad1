export const expectedResponses = {
    QA_BFC_BasePayrollComponent_GET_CLA: {
        year2025: {
            baseForCalculation_1: {
                _embedded: [
                    {
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "origin": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009d1-07e9-0001-1600-030000000000",
                        "inheritanceLevel": {
                            "id": "54a496de-1d36-4b6b-85a1-3ecc63361287",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "origin": {
                            "key": 2,
                            "value": "Uitgezonderd van grondslag"
                        },
                        "payrollComponent": {
                            "category": {
                                "key": 1,
                                "value": "Uren"
                            },
                            "description": "UREN SPEC.BEZ.",
                            "key": 22
                        },
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202503,
                            "periodEndDate": "2025-03-31",
                            "periodNumber": 3,
                            "periodStartDate": "2025-03-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "origin": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009d1-07e9-0001-2400-030000000000",
                        "inheritanceLevel": {
                            "id": "54a496de-1d36-4b6b-85a1-3ecc63361287",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "origin": {
                            "key": 2,
                            "value": "Uitgezonderd van grondslag"
                        },
                        "payrollComponent": {
                            "category": {
                                "key": 2,
                                "value": "Ziekte vangnet"
                            },
                            "description": "UREN NSUP VNET",
                            "key": 36
                        },
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202503,
                            "periodEndDate": "2025-03-31",
                            "periodNumber": 3,
                            "periodStartDate": "2025-03-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "origin": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009d1-07e9-0001-6600-020000000000",
                        "inheritanceLevel": {
                            "id": "54a496de-1d36-4b6b-85a1-3ecc63361287",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "origin": {
                            "key": 1,
                            "value": "Bedragen uit vaste gegevens"
                        },
                        "payrollComponent": {
                            "category": {
                                "key": 30,
                                "value": "Bedrag per eenheid"
                            },
                            "description": "KM.VERGOED.BEL",
                            "key": 102
                        },
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202502,
                            "periodEndDate": "2025-02-28",
                            "periodNumber": 2,
                            "periodStartDate": "2025-02-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "origin": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009d1-07e9-0001-a601-020000000000",
                        "inheritanceLevel": {
                            "id": "54a496de-1d36-4b6b-85a1-3ecc63361287",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "origin": {
                            "key": 1,
                            "value": "Bedragen uit vaste gegevens"
                        },
                        "payrollComponent": {
                            "category": {
                                "key": 12,
                                "value": "Bruto inhouding tabel"
                            },
                            "description": "ANW-PREMIE",
                            "key": 422
                        },
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202502,
                            "periodEndDate": "2025-02-28",
                            "periodNumber": 2,
                            "periodStartDate": "2025-02-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "origin": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009d1-07e9-0001-da01-010000000000",
                        "inheritanceLevel": {
                            "id": "54a496de-1d36-4b6b-85a1-3ecc63361287",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "origin": {
                            "key": 0,
                            "value": "Berekende bedragen in de grondslag"
                        },
                        "payrollComponent": {
                            "category": {
                                "key": 17,
                                "value": "Afdrachtvermindering"
                            },
                            "description": "KRT.KINDEROPV",
                            "key": 474
                        },
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202501,
                            "periodEndDate": "2025-01-31",
                            "periodNumber": 1,
                            "periodStartDate": "2025-01-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "origin": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009d1-07e9-0001-a202-030000000000",
                        "inheritanceLevel": {
                            "id": "54a496de-1d36-4b6b-85a1-3ecc63361287",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "origin": {
                            "key": 2,
                            "value": "Uitgezonderd van grondslag"
                        },
                        "payrollComponent": {
                            "category": {
                                "key": 26,
                                "value": "Output"
                            },
                            "description": "WN.BYDR.AUTO",
                            "key": 674
                        },
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202503,
                            "periodEndDate": "2025-03-31",
                            "periodNumber": 3,
                            "periodStartDate": "2025-03-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "origin": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009d1-07e9-0001-ff02-010000000000",
                        "inheritanceLevel": {
                            "id": "54a496de-1d36-4b6b-85a1-3ecc63361287",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "origin": {
                            "key": 0,
                            "value": "Berekende bedragen in de grondslag"
                        },
                        "payrollComponent": {
                            "category": {
                                "key": 17,
                                "value": "Afdrachtvermindering"
                            },
                            "description": "KORT.LH.LLR",
                            "key": 767
                        },
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202501,
                            "periodEndDate": "2025-01-31",
                            "periodNumber": 1,
                            "periodStartDate": "2025-01-01",
                            "year": 2025
                        },
                        "year": 2025
                    },
                    {
                        "baseForCalculation": {
                            "key": 1
                        },
                        "definedAtLevel": {
                            "id": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            },
                            "origin": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "id": "000009d1-07e9-0001-ff02-020000000000",
                        "inheritanceLevel": {
                            "id": "54a496de-1d36-4b6b-85a1-3ecc63361287",
                            "type": {
                                "key": 1,
                                "value": "CollectiveLaborAgreement"
                            }
                        },
                        "origin": {
                            "key": 0,
                            "value": "Berekende bedragen in de grondslag"
                        },
                        "payrollComponent": {
                            "category": {
                                "key": 17,
                                "value": "Afdrachtvermindering"
                            },
                            "description": "KORT.LH.LLR",
                            "key": 767
                        },
                        "startPayrollPeriod": {
                            "payrollPeriodId": 202502,
                            "periodEndDate": "2025-02-28",
                            "periodNumber": 2,
                            "periodStartDate": "2025-02-01",
                            "year": 2025
                        },
                        "year": 2025
                    }
                ]
            },
        }
    }
};