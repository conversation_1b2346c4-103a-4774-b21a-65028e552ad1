export const collectiveLaborAgreementIds = {
    QA_BaseForCalculation_GET_CLA: "1c0fd70a-be48-42d5-95a9-d1536b643d42",
    QA_BaseForCalculation_POST_CLA: "5bfe800b-ac86-47fc-9a1a-79a9c18d9aca",
};

export const wageModelIds = {
    QA_BaseForCalculation_GET_WM: "df3a7673-df27-4a20-9077-26dcef52ddfd",
    QA_BaseForCalculation_POST_WM: "e00e0220-01c2-4ad8-887b-817a8ec5b5b6",
};

export const payrollAdministrationIds = {
    QA_BaseForCalculation_GET_PA: "4685cf08-be12-4205-aefd-338d808123b5",
    QA_BaseForCalculation_POST_PA: "ca334c95-7c4e-46f7-ac54-8177f49304f5",
};

export const yearIds = {
    QA_BaseForCalculation_GET_CLA: {
        year2025: "000009a6-07e9-0000-0000-000000000000"
    },
    QA_BaseForCalculation_GET_WM: {
        year2025: "000009a7-07e9-0000-0000-000000000000"
    },
    QA_BaseForCalculation_GET_PA: {
        year2025: "000009a8-07e9-0000-0000-000000000000"
    },
};

export const baseForCalculationIds = {
    QA_BaseForCalculation_GET_CLA: {
        year2025: {
            baseForCalculation_1: "000009a6-07e9-0001-0100-000000000000",
        }
    },
    QA_BaseForCalculation_PATCH_CLA: {
        year2025: {
            baseForCalculation_25: "000009b8-07e9-0019-0100-000000000000",
        }
    },
    QA_BaseForCalculation_DELETE_CLA: {
        year2025: {
            baseForCalculation_1: "000009d9-07e9-0001-0100-000000000000",
        }
    },
    QA_BaseForCalculation_DELETE_PA: {
        year2025: {
            baseForCalculation_3: "000009db-07e9-0003-0100-000000000000",
        }
    }
}
