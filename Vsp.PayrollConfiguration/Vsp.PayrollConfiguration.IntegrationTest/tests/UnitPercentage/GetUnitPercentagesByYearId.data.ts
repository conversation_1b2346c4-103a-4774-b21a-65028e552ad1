export const expectedResponses = {
    QA_PayrollConfiguration_CLA: {
        _embedded: [
            {
                "id": "000008fb-07e9-0029-0100-000000000000",
                "inheritanceLevel": {
                    "id": "97c1203c-86bc-4f00-8d89-e5b29df68220",
                    "type": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "payrollComponent": {
                    "key": 41,
                    "description": "CLA UNIT 41",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                "startPayrollPeriod": {
                    "year": 2025,
                    "periodNumber": 1,
                    "payrollPeriodId": 202501,
                    "periodStartDate": "2025-01-01",
                    "periodEndDate": "2025-01-31"
                },
                "definedAtLevel": {
                    "id": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "percentage": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "calculateOver": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "calculateOver": {
                    "key": 0,
                    "value": "Bedrag per eenheid"
                },
                "percentage": 41,
                "year": 2025
            },
            {
                "id": "000008fb-07e9-0029-0300-000000000000",
                "inheritanceLevel": {
                    "id": "97c1203c-86bc-4f00-8d89-e5b29df68220",
                    "type": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "payrollComponent": {
                    "key": 41,
                    "description": "CLA UNIT 41",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                "startPayrollPeriod": {
                    "year": 2025,
                    "periodNumber": 3,
                    "payrollPeriodId": 202503,
                    "periodStartDate": "2025-03-01",
                    "periodEndDate": "2025-03-31"
                },
                "definedAtLevel": {
                    "id": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "percentage": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "calculateOver": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "calculateOver": {
                    "key": 1,
                    "value": "Periode uurloon"
                },
                "percentage": 14,
                "year": 2025
            },
            {
                "id": "000008fb-07e9-002a-0100-000000000000",
                "inheritanceLevel": {
                    "id": "97c1203c-86bc-4f00-8d89-e5b29df68220",
                    "type": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "payrollComponent": {
                    "key": 42,
                    "description": "CLA UNIT 42",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                "startPayrollPeriod": {
                    "year": 2025,
                    "periodNumber": 1,
                    "payrollPeriodId": 202501,
                    "periodStartDate": "2025-01-01",
                    "periodEndDate": "2025-01-31"
                },
                "definedAtLevel": {
                    "id": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "percentage": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "calculateOver": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "calculateOver": {
                    "key": 2,
                    "value": "Gemiddeld uurloon"
                },
                "percentage": 42,
                "year": 2025
            }
        ]
    },
};
