import {testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./DifferentiatedReturnToWorkFund.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrolladministrations/differentiatedreturntoworkfunds/{differentiatedReturnToWorkFundId}",
    method: "PUT",
    params: {
        differentiatedReturnToWorkFundId: entityData.differentiatedReturnToWorkFundIds.QA_DifferentiatedReturnToWorkFund_PUT_PA.Year2025,
    },
    modules: {
        methodNotAllowed: ["PATCH", "POST"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,     // Covered in C# integration tests
        token: "QA_PayrollConfiguration1",
        body: {
            "wga": {
                "totalContribution": 0.5,
                "employmentContribution": 1.401
            },
            "zw": {
                "totalContribution": 1.4
            }
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundId should return 400 and the correct message for wga.employmentContribution > wga.totalContribution": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    differentiatedReturnToWorkFundId: entityData.differentiatedReturnToWorkFundIds.QA_DifferentiatedReturnToWorkFund_PUT_PA.Year2025,
                },
                body: {
                    "wga": {
                        "totalContribution": 1,
                        "employmentContribution": 1.001
                    },
                    "zw": {
                        "totalContribution": 0
                    }
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 1,
                        mode: ValidationMode.count
                    },
                    {
                        key: "messages[0]",
                        value: { "messageCode": "API_PayrollConfiguration_DifferentiatedReturnToWorkFund_WgaTotalContributionPositive_WgaEmploymentContributionGreaterThanHalf" },
                        mode: ValidationMode.partial
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PutDifferentiatedReturnToWorkFundByDifferentiatedReturnToWorkFundId should return 403 when differentiatedReturnToWorkFundId=non-differentiatedReturnToWorkFundGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    differentiatedReturnToWorkFundId: sharedData.nonEntityGuid,
                },
                body: {
                    "wga": {
                        "totalContribution": 1.403,
                        "employmentContribution": 0.6
                    },
                    "zw": {
                        "totalContribution": 1.403
                    }
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonInheritanceGuid,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
