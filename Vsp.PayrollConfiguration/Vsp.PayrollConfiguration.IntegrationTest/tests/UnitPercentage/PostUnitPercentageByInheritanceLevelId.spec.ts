import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./UnitPercentage.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "unitpercentages",
    method: "POST",
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,     // Covered in C# integration tests
        token: "QA_PayrollConfiguration1",
        queryStringParams: {
            collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_UnitPercentage_POST_CLA_ForLevel_CLA,
        },
        body: {
            "calculateOver": {
                "key": 0
            },
            "payrollComponent": {
                "key": 42
            },
            "percentage": 11.995,
            "startPayrollPeriod": {
                "periodNumber": 1
            },
            "year": 2025
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---201---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "PostUnitPercentageByInheritanceLevelId should return 400 and the correct message for non-existing component": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {},
                queryStringParams: {
                    payrollAdministrationId: entityData.payrollAdministrationIds.QA_UnitPercentage_POST_PA_ForLevel_PA,
                },
                body: {
                    "calculateOver": {
                        "key": 0
                    },
                    "payrollComponent": {
                        "key": 52
                    },
                    "percentage": 17.766,
                    "startPayrollPeriod": {
                        "periodNumber": 1
                    },
                    "year": 2025
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 1,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_UnitPercentage_PayrollComponent_Invalid",
                        mode: ValidationMode.strict,
                    },
                ],
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PostUnitPercentageByUnitPercentageId should return 403 when unitPercentageId=non-unitPercentageGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                queryStringParams: {
                    payrollAdministrationId: sharedData.nonEntityGuid,
                },
                body: {
                    "calculateOver": {
                        "key": 0
                    },
                    "payrollComponent": {
                        "key": 42
                    },
                    "percentage": 17.766,
                    "startPayrollPeriod": {
                        "periodNumber": 1
                    },
                    "year": 2025
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonInheritanceGuid,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
