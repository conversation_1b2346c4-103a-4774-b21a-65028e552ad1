# Define input parameters.
param (
    [System.String]
    $Server = "sqlmi-weu-dev-001.4dd48adfb4b8.database.windows.net",

    [System.Management.Automation.PSCredential] 
    [System.Management.Automation.Credential()]
    $Credential = $null,

    [System.String]
    [ValidateNotNullOrEmpty()]
    [ValidateSet('pre', 'post')]
    $Mode = $null
)

Set-StrictMode -Version 3.0
$ErrorActionPreference = "Stop"

# Ask for input parameters if running in user mode.
if ([string]::IsNullOrEmpty($Mode)) { 
    $Mode = Read-Host "Please enter mode (pre/post)"
}
if ($Mode -ne "pre" -and $Mode -ne "post") {
    throw "Invalid mode: $Mode"
}
if ($Credential -eq $null) { $Credential = Get-Credential }

# Show parameters being used.
Write-Host "Using server: $Server"
Write-Host "Using username: $($Credential.UserName)"
Write-Host "Using mode: $Mode"

Import-Module "SqlServer"

# Hard-coded lookup for database keys (part of file name) and associated database names on the server.
$databases = @{
    "loket"       = "VspQA";
}

# Invoke a given SQL script file.
function Invoke-SqlScript {
    param (
        [parameter(Mandatory = $true)][String]$Server,
        [parameter(Mandatory = $true)][System.Management.Automation.PSCredential]$Credential,
        [parameter(Mandatory = $true)][String]$File
    )

    Write-Host "Invoking SQL script: $File"
    $databaseKey = $File.Split('.')[-3]
    $database = $databases[$databaseKey]
    Write-Host "Against database: $databaseKey -> $database"
    Invoke-Sqlcmd -ServerInstance $Server -Database $database -Credential $Credential -InputFile $File -Verbose -OutputSqlErrors $true -AbortOnError -SeverityLevel 0 -ErrorLevel 0
}

# Invoke all SQL script files for a given mode.
function Invoke-SqlScripts {
    param (
        [parameter(Mandatory = $true)][String]$Server,
        [parameter(Mandatory = $true)][System.Management.Automation.PSCredential]$Credential,
        [parameter(Mandatory = $true)][String]$Mode,
        [parameter(Mandatory = $true)][String]$AbsolutePath
    )

    Write-Host "Executing mode: $Mode"
    $filePattern = "*.$Mode.sql"
    Write-Host "File pattern: $filePattern"

    foreach ($file in Get-ChildItem -File -Path $AbsolutePath -Recurse -Filter "$filePattern") {
        Invoke-SqlScript -Server $Server -Credential $Credential -File $file
    }
}

# Determine path to Cypress test files - required SQL scripts are added next to these.
$relativePath = "$PSScriptRoot\..\..\tests"
Write-Host "Relative path: $relativePath"
$absolutePath = [System.IO.Path]::GetFullPath($relativePath)
Write-Host "Absolute path: $absolutePath"

# In pre mode we also run post mode to cleanup any leftover test data.
if ($Mode -eq "pre") {
    Invoke-SqlScripts -Server $Server -Credential $Credential -Mode "post" -AbsolutePath $absolutePath
    Invoke-SqlScripts -Server $Server -Credential $Credential -Mode "pre" -AbsolutePath $absolutePath
}
elseif ($Mode -eq "post") {
    Invoke-SqlScripts -Server $Server -Credential $Credential -Mode "post" -AbsolutePath $absolutePath
}
