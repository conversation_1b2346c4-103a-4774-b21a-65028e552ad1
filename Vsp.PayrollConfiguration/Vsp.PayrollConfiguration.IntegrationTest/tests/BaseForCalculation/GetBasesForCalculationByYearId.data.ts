export const expectedResponses = {
    QA_BaseForCalculation_GET_CLA: {
        year2025: {
            _embedded: [
                {
                    "accrualEndPayrollPeriod": {
                        "periodNumber": 2
                    },
                    "advancePayrollComponent": {
                        "category": {
                            "key": 9,
                            "value": "Netto betaling"
                        },
                        "description": "REISKST ONBEL",
                        "key": 367
                    },
                    "advancePayrollPeriod": {
                        "periodNumber": 1
                    },
                    "advancePercentage": 1.101011,
                    "baseType": null,
                    "calculationPayrollPeriod": {
                        "periodNumber": 1
                    },
                    "definedAtLevel": {
                        "accrualEndPayrollPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "baseType": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "calculationPayrollPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "description": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "endEmployeeAge": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "endEmployeeAgeType": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "financialMarkupPercentage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "financialReservationPercentage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "id": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isAutomaticCalculation": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isCumulativeCalculation": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isPartTimeCalculation": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isPayoutAtEndOfEmployment": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isPayoutAtStartOfEmployment": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isSupplementingDailyWage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "minimumMaximumType": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "payoutPayrollPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "payslipType": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "percentage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "periodicReservationPayrollComponent": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "referencePayrollPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "reservationPayrollComponent": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "reservationPayrollPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "reservationPercentage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "resultPayrollComponent": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "startEmployeeAge": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "startEmployeeAgeType": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "description": "BFC_CLA_1_1",
                    "endEmployeeAge": 0,
                    "endEmployeeAgeType": null,
                    "financialMarkupPercentage": 1.000111,
                    "financialReservationPercentage": 1.000001,
                    "id": "000009a6-07e9-0001-0100-000000000000",
                    "inheritanceLevel": {
                        "id": "1c0fd70a-be48-42d5-95a9-d1536b643d42",
                        "type": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "isAutomaticCalculation": false,
                    "isCumulativeCalculation": true,
                    "isPartTimeCalculation": false,
                    "isPayoutAtEndOfEmployment": false,
                    "isPayoutAtStartOfEmployment": false,
                    "isSupplementingDailyWage": false,
                    "key": 1,
                    "minimumMaximumType": {
                        "key": 2,
                        "value": "Resultaat"
                    },
                    "payoutPayrollPeriod": {
                        "periodNumber": 2
                    },
                    "payslipType": {
                        "key": 4,
                        "value": "Normale strook"
                    },
                    "percentage": 1.111111,
                    "periodicReservationPayrollComponent": {
                        "category": {
                            "key": 6,
                            "value": "Eenheden"
                        },
                        "description": "KM.ONBELAST",
                        "key": 41
                    },
                    "referencePayrollPeriod": null,
                    "resultPayrollComponent": {
                        "category": {
                            "key": 9,
                            "value": "Netto betaling"
                        },
                        "description": "ONKSTVRG.ONBEL",
                        "key": 366
                    },
                    "startEmployeeAge": 0,
                    "startEmployeeAgeType": null,
                    "startPayrollPeriod": {
                        "payrollPeriodId": 202501,
                        "periodEndDate": "2025-01-31",
                        "periodNumber": 1,
                        "periodStartDate": "2025-01-01",
                        "year": 2025
                    },
                    "year": 2025
                },
                {
                    "accrualEndPayrollPeriod": {
                        "periodNumber": 4
                    },
                    "advancePayrollComponent": {
                        "category": {
                            "key": 8,
                            "value": "Bruto vergoeding tarief"
                        },
                        "description": "PROVISIE",
                        "key": 315
                    },
                    "advancePayrollPeriod": {
                        "periodNumber": 4
                    },
                    "advancePercentage": 0.110002,
                    "baseType": {
                        "key": 1,
                        "value": "Vakantiebijslag"
                    },
                    "calculationPayrollPeriod": {
                        "periodNumber": 1
                    },
                    "definedAtLevel": {
                        "accrualEndPayrollPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "baseType": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "calculationPayrollPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "description": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "endEmployeeAge": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "endEmployeeAgeType": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "financialMarkupPercentage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "financialReservationPercentage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "id": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isAutomaticCalculation": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isCumulativeCalculation": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isPartTimeCalculation": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isPayoutAtEndOfEmployment": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isPayoutAtStartOfEmployment": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isSupplementingDailyWage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "minimumMaximumType": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "payoutPayrollPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "payslipType": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "percentage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "periodicReservationPayrollComponent": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "referencePayrollPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "reservationPayrollComponent": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "reservationPayrollPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "reservationPercentage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "resultPayrollComponent": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "startEmployeeAge": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "startEmployeeAgeType": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "description": "BFC_CLA_1_2",
                    "endEmployeeAge": 0.5,
                    "endEmployeeAgeType": {
                        "key": 2,
                        "value": "Eerste van de periode NA bereiken leeftijd"
                    },
                    "financialMarkupPercentage": 0.000002,
                    "financialReservationPercentage": 0.000002,
                    "id": "000009a6-07e9-0001-0200-000000000000",
                    "inheritanceLevel": {
                        "id": "1c0fd70a-be48-42d5-95a9-d1536b643d42",
                        "type": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "isAutomaticCalculation": false,
                    "isCumulativeCalculation": true,
                    "isPartTimeCalculation": false,
                    "isPayoutAtEndOfEmployment": false,
                    "isPayoutAtStartOfEmployment": false,
                    "isSupplementingDailyWage": false,
                    "key": 1,
                    "minimumMaximumType": {
                        "key": 2,
                        "value": "Resultaat"
                    },
                    "payoutPayrollPeriod": {
                        "periodNumber": 3
                    },
                    "payslipType": {
                        "key": 4,
                        "value": "Normale strook"
                    },
                    "percentage": 0.110002,
                    "periodicReservationPayrollComponent": {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN GEWERKT",
                        "key": 1
                    },
                    "referencePayrollPeriod": null,
                    "resultPayrollComponent": {
                        "category": {
                            "key": 6,
                            "value": "Eenheden"
                        },
                        "description": "KM.ONBELAST",
                        "key": 41
                    },
                    "startEmployeeAge": 0.25,
                    "startEmployeeAgeType": {
                        "key": 1,
                        "value": "Eerste van de periode VAN bereiken leeftijd"
                    },
                    "startPayrollPeriod": {
                        "payrollPeriodId": 202502,
                        "periodEndDate": "2025-02-28",
                        "periodNumber": 2,
                        "periodStartDate": "2025-02-01",
                        "year": 2025
                    },
                    "year": 2025
                }
            ]
        },
    }
};