import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./Shift.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "shifts/{shiftId}",
    params: {
        shiftId: entityData.shiftIds.QA_PayrollConfiguration_CLA_ForLevel_CLA.shift01,
    },
    method: "PATCH",
    modules: {
        methodNotAllowed: ["GET", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,     // Covered in C# integration tests
        token: "QA_PayrollConfiguration1",
        body: {
            "shiftNumber": 1,
            "startPayrollPeriod": {
                "year": 2025,
                "periodNumber": 1
            },
            "fullTimeHoursPerWeek": 8.23,
            "bonusPercentage": 9.45
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger validation
        "PatchShiftByShiftId should return 400 and the correct message when bonusPercentage>100": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    shiftId: entityData.shiftIds.QA_PayrollConfiguration_CLA_ForLevel_CLA.shift01,
                },
                queryStringParams: {},
                body: {
                    "fullTimeHoursPerWeek": 8.12,
                    "bonusPercentage": 9999.99
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 1,
                        mode: ValidationMode.count
                    },
                    {
                        key: "messages[0].messageCode",
                        value: "ModelStateValidationError",
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "messages[0].properties",
                        value: "{\"bonusPercentage\":\"BonusPercentage must have a value between '0' and '100'.\"}",
                        mode: ValidationMode.strict,
                    },
                ]
            }
        },

        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PatchShiftByShiftId should return 403 when shiftId=non-shiftId": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    shiftId: sharedData.nonEntityGuid,
                },
                queryStringParams: {},
                body: {
                    "fullTimeHoursPerWeek": 8.12,
                    "bonusPercentage": 24.83
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
