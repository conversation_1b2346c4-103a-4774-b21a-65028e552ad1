{"author": {"name": "VSP"}, "browser": {"fs": false, "path": false, "os": false}, "description": "Vsp.PayrollConfiguration.IntegrationTest", "dependencies": {"@vsp/qa-api-testing": "^2.0.6"}, "devDependencies": {"@cypress/webpack-preprocessor": "^5.10.0", "@types/node": "^8.0.14", "cypress": "^13.15.0", "eslint": "^9.17.0", "typescript": "^4.7.4"}, "main": "app.js", "name": "vsp.payrollconfiguration.integration-test", "scripts": {"loketauth": "vsts-npm-auth -config .npmrc", "build": "tsc --build", "clean": "tsc --build --clean", "cypress:open": "cypress open --e2e", "cypress:open:local": "cypress open --e2e --env ENVIRONMENT=1", "cypress:run": "cypress run"}, "version": "0.0.0"}