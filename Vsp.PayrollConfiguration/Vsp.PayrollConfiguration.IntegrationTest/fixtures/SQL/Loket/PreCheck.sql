--For use in DevOps
:setvar fixturespath "#{System.DefaultWorkingDirectory}#\#{SqlFixturesPath}#"
:setvar testspath "#{System.DefaultWorkingDirectory}#\#{SqlTestsPath}#"

--Remove test data from any previous runs
:r $(fixturespath)\SQL\Loket\PostCheck.sql

PRINT 'START PreCheckLoket'

-- Add calls to your SQL-scripts here. For example:
-- :r $(testspath)\Shift\Shift.pre.sql

PRINT 'END PreCheckLoket'
