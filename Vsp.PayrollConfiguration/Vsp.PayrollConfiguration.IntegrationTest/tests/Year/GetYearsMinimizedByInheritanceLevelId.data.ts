export const expectedResponses = {
    QA_Year_GET_CLA: {
        _embedded: [
            { "id": "0000093b-07e5-0000-0000-000000000000", "inheritanceLevel": { "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1", "type": { "key": 1, "value": "CollectiveLaborAgreement" } }, "payrollPeriodType": { "key": 1, "value": "Maand" }, "year": 2021 },
            { "id": "0000093b-07e6-0000-0000-000000000000", "inheritanceLevel": { "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1", "type": { "key": 1, "value": "CollectiveLaborAgreement" } }, "payrollPeriodType": { "key": 1, "value": "Maand" }, "year": 2022 },
            { "id": "0000093b-07e7-0000-0000-000000000000", "inheritanceLevel": { "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1", "type": { "key": 1, "value": "CollectiveLaborAgreement" } }, "payrollPeriodType": { "key": 1, "value": "Maand" }, "year": 2023 },
            { "id": "0000093b-07e8-0000-0000-000000000000", "inheritanceLevel": { "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1", "type": { "key": 1, "value": "CollectiveLaborAgreement" } }, "payrollPeriodType": { "key": 1, "value": "Maand" }, "year": 2024 },
            { "id": "0000093b-07e9-0000-0000-000000000000", "inheritanceLevel": { "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1", "type": { "key": 1, "value": "CollectiveLaborAgreement" } }, "payrollPeriodType": { "key": 1, "value": "Maand" }, "year": 2025 }
        ]
    },
    QA_Year_GET_WM: {
        _embedded: [
            { "id": "0000093c-07e5-0000-0000-000000000000", "inheritanceLevel": { "id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": { "key": 2, "value": "WageModel" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2021 },
            { "id": "0000093c-07e6-0000-0000-000000000000", "inheritanceLevel": { "id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": { "key": 2, "value": "WageModel" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2022 },
            { "id": "0000093c-07e7-0000-0000-000000000000", "inheritanceLevel": { "id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": { "key": 2, "value": "WageModel" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2023 },
            { "id": "0000093c-07e8-0000-0000-000000000000", "inheritanceLevel": { "id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": { "key": 2, "value": "WageModel" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2024 },
            { "id": "0000093c-07e9-0000-0000-000000000000", "inheritanceLevel": { "id": "458d0541-8bdb-4d35-8c6e-606297d15db8", "type": { "key": 2, "value": "WageModel" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2025 }
        ]
    },
    QA_Year_GET_PA: {
        _embedded: [
            { "id": "0000093d-07e4-0000-0000-000000000000", "inheritanceLevel": { "id": "d8706816-6153-455a-934d-4e939a2af6c3", "type": { "key": 3, "value": "PayrollAdministration" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2020 },
            { "id": "0000093d-07e5-0000-0000-000000000000", "inheritanceLevel": { "id": "d8706816-6153-455a-934d-4e939a2af6c3", "type": { "key": 3, "value": "PayrollAdministration" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2021 },
            { "id": "0000093d-07e6-0000-0000-000000000000", "inheritanceLevel": { "id": "d8706816-6153-455a-934d-4e939a2af6c3", "type": { "key": 3, "value": "PayrollAdministration" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2022 },
            { "id": "0000093d-07e7-0000-0000-000000000000", "inheritanceLevel": { "id": "d8706816-6153-455a-934d-4e939a2af6c3", "type": { "key": 3, "value": "PayrollAdministration" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2023 },
            { "id": "0000093d-07e8-0000-0000-000000000000", "inheritanceLevel": { "id": "d8706816-6153-455a-934d-4e939a2af6c3", "type": { "key": 3, "value": "PayrollAdministration" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2024 },
            { "id": "0000093d-07e9-0000-0000-000000000000", "inheritanceLevel": { "id": "d8706816-6153-455a-934d-4e939a2af6c3", "type": { "key": 3, "value": "PayrollAdministration" } }, "payrollPeriodType": { "key": 3, "value": "4 Weken" }, "year": 2025 }
        ]
    }
};
