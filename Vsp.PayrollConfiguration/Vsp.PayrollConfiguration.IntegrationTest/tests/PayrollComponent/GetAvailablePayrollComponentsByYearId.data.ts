export const expectedResponses = {
    QA_AvailableComponents_GET_CLA: {
        year2025: {
            "_embedded": [
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN GEWERKT",
                    "key": 1
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN VAKANTIE",
                    "key": 2
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN BET.VERZ.",
                    "key": 3
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN ONB.VERZ.",
                    "key": 4
                },
                {
                    "category": {
                        "key": 5,
                        "value": "Onregelmatige dienst"
                    },
                    "description": "UREN ONR.1",
                    "key": 5
                },
                {
                    "category": {
                        "key": 5,
                        "value": "Onregelmatige dienst"
                    },
                    "description": "UREN ONR.2",
                    "key": 6
                },
                {
                    "category": {
                        "key": 5,
                        "value": "Onregelmatige dienst"
                    },
                    "description": "UREN ONR.3",
                    "key": 7
                },
                {
                    "category": {
                        "key": 5,
                        "value": "Onregelmatige dienst"
                    },
                    "description": "UREN ONR.4",
                    "key": 8
                },
                {
                    "category": {
                        "key": 5,
                        "value": "Onregelmatige dienst"
                    },
                    "description": "UREN ONR.5",
                    "key": 9
                },
                {
                    "category": {
                        "key": 4,
                        "value": "Tijd voor tijd"
                    },
                    "description": "UREN TVT 1",
                    "key": 10
                },
                {
                    "category": {
                        "key": 4,
                        "value": "Tijd voor tijd"
                    },
                    "description": "UREN TVT 2",
                    "key": 11
                },
                {
                    "category": {
                        "key": 4,
                        "value": "Tijd voor tijd"
                    },
                    "description": "UREN TVT 3",
                    "key": 12
                },
                {
                    "category": {
                        "key": 4,
                        "value": "Tijd voor tijd"
                    },
                    "description": "UREN TVT 4",
                    "key": 13
                },
                {
                    "category": {
                        "key": 4,
                        "value": "Tijd voor tijd"
                    },
                    "description": "UREN TVT 5",
                    "key": 14
                },
                {
                    "category": {
                        "key": 3,
                        "value": "Overwerk"
                    },
                    "description": "UREN OVW 1",
                    "key": 15
                },
                {
                    "category": {
                        "key": 3,
                        "value": "Overwerk"
                    },
                    "description": "UREN OVW 2",
                    "key": 16
                },
                {
                    "category": {
                        "key": 3,
                        "value": "Overwerk"
                    },
                    "description": "UREN OVW 3",
                    "key": 17
                },
                {
                    "category": {
                        "key": 3,
                        "value": "Overwerk"
                    },
                    "description": "UREN OVW 4",
                    "key": 18
                },
                {
                    "category": {
                        "key": 3,
                        "value": "Overwerk"
                    },
                    "description": "UREN OVW 5",
                    "key": 19
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN REIS BINN",
                    "key": 20
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN REIS BUIT",
                    "key": 21
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN SPEC.BEZ.",
                    "key": 22
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN VV",
                    "key": 23
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN WWVV",
                    "key": 24
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN WAO/WIA",
                    "key": 25
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN WW",
                    "key": 26
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN NAB.VERLF",
                    "key": 27
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "MEERUREN",
                    "key": 28
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN TSF",
                    "key": 29
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "ATV UREN",
                    "key": 30
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "OPGENOMEN TVT",
                    "key": 31
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN VERZ. CAO",
                    "key": 32
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN FEESTDGN.",
                    "key": 33
                },
                {
                    "category": {
                        "key": 2,
                        "value": "Ziekte vangnet"
                    },
                    "description": "UREN ZIEK VNET",
                    "key": 34
                },
                {
                    "category": {
                        "key": 2,
                        "value": "Ziekte vangnet"
                    },
                    "description": "WACHTUREN VNET",
                    "key": 35
                },
                {
                    "category": {
                        "key": 2,
                        "value": "Ziekte vangnet"
                    },
                    "description": "UREN NSUP VNET",
                    "key": 36
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN ZIEK",
                    "key": 37
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "WACHTUREN ZIEK",
                    "key": 38
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN NSUP ZIEK",
                    "key": 39
                },
                {
                    "category": {
                        "key": 1,
                        "value": "Uren"
                    },
                    "description": "UREN ZIEK >130",
                    "key": 40
                },
                {
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    },
                    "description": "KM.ONBELAST",
                    "key": 41
                },
                {
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    },
                    "description": "KM.BELAST",
                    "key": 42
                },
                {
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    },
                    "description": "EENHEID 3",
                    "key": 43
                },
                {
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    },
                    "description": "EENHEID 4",
                    "key": 44
                },
                {
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    },
                    "description": "EENHEID 5",
                    "key": 45
                },
                {
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    },
                    "description": "EENHEID 6",
                    "key": 46
                },
                {
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    },
                    "description": "EENHEID 7",
                    "key": 47
                },
                {
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    },
                    "description": "EENHEID 8",
                    "key": 48
                },
                {
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    },
                    "description": "EENHEID 9",
                    "key": 49
                },
                {
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    },
                    "description": "EENHEID 10",
                    "key": 50
                }
            ]
        },
        year2025_withFilter_lastPage: {
            "_embedded": [
                {
                    "category": {
                        "key": 21,
                        "value": "ABP"
                    },
                    "description": "KRT.ZIEK",
                    "key": 312
                },
                {
                    "category": {
                        "key": 21,
                        "value": "ABP"
                    },
                    "description": "ABP EXTRA PENS",
                    "key": 475
                }
            ]
        }
    }
}