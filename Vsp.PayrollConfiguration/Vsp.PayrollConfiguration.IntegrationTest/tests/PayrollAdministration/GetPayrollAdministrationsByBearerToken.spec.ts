import { ITestConfig, testEndpoint, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as endpointData from "./GetPayrollAdministrationsByBearerToken.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrolladministrations",
    method: "GET",
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_AKPayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
        QA_AKAdministrationFilter: sharedData.users.QA_AKAdministrationFilter,
        QA_AdministrationFilter_Year_User: sharedData.users.QA_AdministrationFilter_Year_User,
    },
    tests: {
        // ---200---
        "GetPayrollAdministrationsByBearerToken should return 200, the correct resourceVersion+obsoleteDate and the correct list of Payroll Administrations to provider user QA_AKPayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                queryStringParams: {
                    filter: "name lk 'QA_PayrollConfiguration1_'",
                    orderBy: "name",
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: 5,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        // request with x-reportinput not necessary, covered in C# integration tests
        // ---BFL3-15975 (global administration filter)---
        "GetPayrollAdministrationsByBearerToken Voorbereiding: Set filter for QA_AKAdministrationFilter to Period Type 'Week'": {
            request: {
                serviceUrl: sharedData.baseConfig.globalFilterUrl,
                route: "user/filtersettings/payrolladministrations",
                method: "PUT",
                headers: sharedData.requestHeaders,
                token: "QA_AKAdministrationFilter",
                params: {
                    version: "2",
                },
                body: {
                    "filterOnCollectiveLaborAgreements": [],
                    "filterOnWageModels": [{ key: 1070, value: "QA_WageModel02FourWeeks" }],
                    "filterOnGroupCodes": [],
                    "filterOnGroupClassifications": [],
                }
            },
            response: {
                code: 200
            }
        },
        "GetPayrollAdministrationsByBearerToken should return 200 and global filtered list for provider user QA_AKAdministrationFilter": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKAdministrationFilter",
                queryStringParams: {
                    orderBy: "name",
                    filter: "employer.companyName eq 'QA_PayrollProcessOverviewPaww'"
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "totalSize",
                        value: 2,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "_embedded",
                        value: 2,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded[0].id",
                        value: "2fb31ce6-1540-4cd1-b49e-0a2818a27a5e",
                        mode: ValidationMode.strict
                    },
                    {
                        key: "_embedded[1].id",
                        value: "b3bbed07-1679-4373-a348-50922b7cf6c5",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        "GetPayrollAdministrationsByBearerToken Afronding: Reset filter for QA_AKAdministrationFilter": {
            request: {
                serviceUrl: sharedData.baseConfig.globalFilterUrl,
                route: "user/filtersettings/payrolladministrations",
                method: "PUT",
                headers: sharedData.requestHeaders,
                token: "QA_AKAdministrationFilter",
                params: {
                    version: "2",
                },
                body: {
                    filterOnCollectiveLaborAgreements: [],
                    filterOnWageModels: [],
                    filterOnGroupCodes: [],
                    filterOnGroupClassifications: [],
                },
            },
            response: {
                code: 200
            }
        },
        // ---end BFL3-15975 (global administration filter)---
        // --- BFL3-18042 (global administration filter)---
        "GetPayrollAdministrationsByBearerToken Voorbereiding: Set filter for QA_AdministrationFilter_Year_User to Year '2020'": {
            request: {
                serviceUrl: sharedData.baseConfig.globalFilterUrl,
                route: "user/filtersettings/payrolladministrations",
                method: "PUT",
                headers: sharedData.requestHeaders,
                token: "QA_AdministrationFilter_Year_User",
                params: {
                    version: "2",
                },
                body: {
                    "filterOnCollectiveLaborAgreements": [],
                    "filterOnWageModels": [],
                    "filterOnGroupCodes": [],
                    "filterOnGroupClassifications": [],
                    "filterOnYear": { key: 2020 },
                }
            },
            response: {
                code: 200
            }
        },
        "GetPayrollAdministrationsByBearerToken should return 200 and global filtered list for provider user QA_AdministrationFilter_Year_User with year filter on 2020": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AdministrationFilter_Year_User",
                queryStringParams: {
                    orderBy: "name,id",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "totalSize",
                        value: 2,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "_embedded",
                        value: 2,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded[0].id",
                        value: "02f4c2ff-e53a-406d-8f65-11508fe06bc6", // QA_AdministrationFilter_Year_2025
                        mode: ValidationMode.strict
                    },
                    {
                        key: "_embedded[1].id",
                        value: "e94145c9-df1f-4788-8c72-6be92da62693", // QA_AdministrationFilter_Year_2020
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        "GetPayrollAdministrationsByBearerToken Voorbereiding: Set filter for QA_AdministrationFilter_Year_User to Year '2025'": {
            request: {
                serviceUrl: sharedData.baseConfig.globalFilterUrl,
                route: "user/filtersettings/payrolladministrations",
                method: "PUT",
                headers: sharedData.requestHeaders,
                token: "QA_AdministrationFilter_Year_User",
                params: {
                    version: "2",
                },
                body: {
                    "filterOnCollectiveLaborAgreements": [],
                    "filterOnWageModels": [],
                    "filterOnGroupCodes": [],
                    "filterOnGroupClassifications": [],
                    "filterOnYear": { key: 2025 },
                }
            },
            response: {
                code: 200
            }
        },
        "GetPayrollAdministrationsByBearerToken should return 200 and global filtered list for provider user QA_AdministrationFilter_Year_User with year filter on 2025": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AdministrationFilter_Year_User",
                queryStringParams: {
                    orderBy: "name",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "totalSize",
                        value: 1,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "_embedded",
                        value: 1,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded[0].id",
                        value: "02f4c2ff-e53a-406d-8f65-11508fe06bc6", // QA_AdministrationFilter_Year_2025
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        "GetPayrollAdministrationsByBearerToken Voorbereiding: Clear filter for QA_AdministrationFilter_Year_User": {
            request: {
                serviceUrl: sharedData.baseConfig.globalFilterUrl,
                route: "user/filtersettings/payrolladministrations",
                method: "PUT",
                headers: sharedData.requestHeaders,
                token: "QA_AdministrationFilter_Year_User",
                params: {
                    version: "2",
                },
                body: {
                    "filterOnCollectiveLaborAgreements": [],
                    "filterOnWageModels": [],
                    "filterOnGroupCodes": [],
                    "filterOnGroupClassifications": [],
                    "filterOnYear": null,
                }
            },
            response: {
                code: 200
            }
        },
        "GetPayrollAdministrationsByBearerToken should return 200 and global filtered list for provider user QA_AdministrationFilter_Year_User with no filter": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AdministrationFilter_Year_User",
                queryStringParams: {
                    orderBy: "name",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "totalSize",
                        value: 3,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "_embedded",
                        value: 3,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded[0].id",
                        value: "7d19d7d0-a83d-4db9-aa2e-51b1ebeed291", // QA_AdministrationFilter_Year_2019
                        mode: ValidationMode.strict
                    },
                    {
                        key: "_embedded[1].id",
                        value: "e94145c9-df1f-4788-8c72-6be92da62693", // QA_AdministrationFilter_Year_2020
                        mode: ValidationMode.strict
                    },
                    {
                        key: "_embedded[2].id",
                        value: "02f4c2ff-e53a-406d-8f65-11508fe06bc6", // QA_AdministrationFilter_Year_2025
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // --- end BFL3-18042 (global administration filter)---
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
