import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./PayrollComponent.data";
import * as endpointData from "./GetAvailablePayrollComponentsByYearId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "years/{yearId}/payrollcomponents/available",
    method: "GET",
    params: {
        yearId: entityData.yearIds.QA_PayrollComponent_GET_CLA.year2025,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        "GetAvailablePayrollComponentsByYearId (CLA) should return 200, the correct resourceVersion+obsoleteDate, and the correct list of Available Components for 2025 at collective labor agreement level to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_PayrollComponent_GET_CLA.year2025,
                },
                queryStringParams: {
                    pageSize: "50",
                    orderBy: "key",
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: 1428,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_AvailableComponents_GET_CLA.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_AvailableComponents_GET_CLA.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        "GetAvailablePayrollComponentsByYearId (CLA) should return 200, the correct resourceVersion+obsoleteDate, and the correct list of Available Components for 2025 at collective labor agreement level, on a specific page with a custom filter to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_PayrollComponent_GET_CLA.year2025,
                },
                queryStringParams: {
                    filter: "category.value lk 'abp'",
                    orderBy: "key",
                    pageSize: "10",
                    pageNumber: "2"
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: 12,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "pageSize",
                        value: 10,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "totalPages",
                        value: 2,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_AvailableComponents_GET_CLA.year2025_withFilter_lastPage._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_AvailableComponents_GET_CLA.year2025_withFilter_lastPage._embedded,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        // request with x-reportinput not necessary, covered in C# integration tests
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetAvailablePayrollComponentsByYearId should return 403 when yearId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});