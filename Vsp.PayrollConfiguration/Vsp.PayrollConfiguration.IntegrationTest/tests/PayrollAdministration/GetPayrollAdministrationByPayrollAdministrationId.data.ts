export const expectedResponses = {
    QA_PayrollAdministration1: {
        content: {
            "id": "bbe616a2-4e59-4d39-8165-ee37a9708be9",
            "name": "QA_PayrollConfiguration1_PA_ForLevel_PA",
            "clientNumber": "101611",
            "employer": {
                "id": "6748429c-bcb6-4afa-9715-27d4bf00256d",
                "companyName": "QA_PayrollConfiguration1"
            },
            "administrationNumber": null,
            "groupCode": 1337,
            "collectiveLaborAgreement": {
                "id": "dfdaa0a5-bce8-4ddd-8e1c-42554334c342",
                "description": "QA_PayrollConfiguration1_CLA_ForLevel_PA",
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
            },
            "wageModel": {
                "id": "5e010bed-611d-469b-a220-5253e3896f47",
                "description": "QA_PayrollConfiguration1_WM_ForLevel_PA",
                "comment": "Empty WM for unit and integration tests. DO NOT EDIT!",
                "collectiveLaborAgreement": {
                    "id": "dfdaa0a5-bce8-4ddd-8e1c-42554334c342",
                    "description": "QA_PayrollConfiguration1_CLA_ForLevel_PA",
                    "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
                }
            }
        }
    },
    QA_PayrollAdministration2: {
        content: {
            "id": "8d20dffc-49b6-454c-93e1-574d6a4dcb06",
            "name": "QA_PayrollConfiguration2_PA_ForLevel_PA",
            "clientNumber": "101612",
            "employer": {
                "id": "f22758a4-9475-41f0-9519-2070106b4267",
                "companyName": "QA_PayrollConfiguration2"
            },
            "administrationNumber": null,
            "groupCode": 1337,
            "collectiveLaborAgreement": {
                "id": "9ad57900-980e-404a-b8a3-14eca3278552",
                "description": "QA_PayrollConfiguration2_CLA_ForLevel_PA",
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
            },
            "wageModel": {
                "id": "b2dd38be-45fa-4a64-b21e-df1911cc3e07",
                "description": "QA_PayrollConfiguration2_WM_ForLevel_PA",
                "comment": "Empty WM for unit and integration tests. DO NOT EDIT!",
                "collectiveLaborAgreement": {
                    "id": "9ad57900-980e-404a-b8a3-14eca3278552",
                    "description": "QA_PayrollConfiguration2_CLA_ForLevel_PA",
                    "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
                }
            }
        }
    },
};
