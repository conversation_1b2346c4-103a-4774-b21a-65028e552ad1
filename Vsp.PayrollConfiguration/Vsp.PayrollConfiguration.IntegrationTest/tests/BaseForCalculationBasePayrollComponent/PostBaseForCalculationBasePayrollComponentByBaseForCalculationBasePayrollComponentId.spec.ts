import {ITestConfig, testEndpoint, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculationBasePayrollComponent.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "basesforcalculation/{baseForCalculationId}/basepayrollcomponents",
    method: "POST",
    params: {
        baseForCalculationId: entityData.baseForCalculationIds.QA_BFC_BasePayrollComponent_GET_CLA_2025.year2025.baseForCalculation_1,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,
        token: "QA_PayrollConfiguration1",
        body: {
            "payrollComponent": { "key": 1 },
            "origin": { "key": 0 },
            "startPayrollPeriod": {
                "periodNumber": 1
            },
            "year": 2025
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---201---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "PostBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentId should return 400 and the correct message for invalid Origin value": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: entityData.baseForCalculationIds.QA_BFC_BasePayrollComponent_GET_CLA_2025.year2025.baseForCalculation_1,
                },
                timeout: 120000,
                body: {
                    "payrollComponent": { "key": 1 },
                    "origin": { "key": 5 },
                    "startPayrollPeriod": {
                        "periodNumber": 1
                    },
                    "year": 2025
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_Invalid",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---400---
        // just one check to trigger a validation for invalid payroll component
        "PostBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentId should return 400 and the correct message for invalid PayrollComponent key": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: entityData.baseForCalculationIds.QA_BFC_BasePayrollComponent_GET_CLA_2025.year2025.baseForCalculation_1,
                },
                timeout: 120000,
                body: {
                    "payrollComponent": { "key": 9999999 },
                    "origin": { "key": 0 },
                    "startPayrollPeriod": {
                        "periodNumber": 1
                    },
                    "year": 2025
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages[0]",
                        value: {
                            "messageCode": "API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Post_Invalid"
                        },
                        mode: ValidationMode.partial
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PostBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentId should return 403 when baseForCalculationId=non-baseForCalculationId": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: sharedData.nonEntityGuid,
                },
                body: {
                    "payrollComponent": { "key": 1 },
                    "origin": { "key": 0 },
                    "startPayrollPeriod": {
                        "periodNumber": 1
                    },
                    "year": 2025
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
