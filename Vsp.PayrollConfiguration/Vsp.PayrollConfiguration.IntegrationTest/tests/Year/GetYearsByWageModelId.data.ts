export const expectedResponses = {
    QA_Year_GET_WM: {
        _embedded: [
            {
                "id": "0000093c-07e4-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "458d0541-8bdb-4d35-8c6e-606297d15db8",
                    "type": {
                        "key": 2,
                        "value": "WageModel"
                    }
                },
                "year": 2020,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 15,
                    "fullTimeHoursPerWeek": 15.25,
                    "bonusPercentage": 15.025
                },
                "standardEmployeeProfile": {
                    "employeeProfileNumber": 1,
                    "description": "WM employee profile 2020"
                },
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardEmployeeProfile": {
                        "key": 2,
                        "value": "WageModel"
                    }
                }
            },
            {
                "id": "0000093c-07e5-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "458d0541-8bdb-4d35-8c6e-606297d15db8",
                    "type": {
                        "key": 2,
                        "value": "WageModel"
                    }
                },
                "year": 2021,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 1,
                    "fullTimeHoursPerWeek": 40.21,
                    "bonusPercentage": 0.021
                },
                "standardEmployeeProfile": null,
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardEmployeeProfile": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                }
            },
            {
                "id": "0000093c-07e6-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "458d0541-8bdb-4d35-8c6e-606297d15db8",
                    "type": {
                        "key": 2,
                        "value": "WageModel"
                    }
                },
                "year": 2022,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": null,
                "standardEmployeeProfile": null,
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardEmployeeProfile": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                }
            },
            {
                "id": "0000093c-07e7-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "458d0541-8bdb-4d35-8c6e-606297d15db8",
                    "type": {
                        "key": 2,
                        "value": "WageModel"
                    }
                },
                "year": 2023,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 2,
                    "fullTimeHoursPerWeek": 38.23,
                    "bonusPercentage": 5.023
                },
                "standardEmployeeProfile": {
                    "employeeProfileNumber": 1,
                    "description": "WM employee profile 2023"
                },
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardEmployeeProfile": {
                        "key": 2,
                        "value": "WageModel"
                    }
                }
            },
            {
                "id": "0000093c-07e8-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "458d0541-8bdb-4d35-8c6e-606297d15db8",
                    "type": {
                        "key": 2,
                        "value": "WageModel"
                    }
                },
                "year": 2024,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 2,
                    "fullTimeHoursPerWeek": 38.24,
                    "bonusPercentage": 5.024
                },
                "standardEmployeeProfile": {
                    "employeeProfileNumber": 1,
                    "description": "WM employee profile 2024"
                },
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardEmployeeProfile": {
                        "key": 2,
                        "value": "WageModel"
                    }
                }
            },
            {
                "id": "0000093c-07e9-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "458d0541-8bdb-4d35-8c6e-606297d15db8",
                    "type": {
                        "key": 2,
                        "value": "WageModel"
                    }
                },
                "year": 2025,
                "payrollPeriodType": {
                    "key": 3,
                    "value": "4 Weken"
                },
                "standardShift": {
                    "shiftNumber": 11,
                    "fullTimeHoursPerWeek": 38.25,
                    "bonusPercentage": 5.025
                },
                "standardEmployeeProfile": {
                    "employeeProfileNumber": 1,
                    "description": "WM employee profile 2025"
                },
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardShift": {
                        "key": 2,
                        "value": "WageModel"
                    },
                    "standardEmployeeProfile": {
                        "key": 2,
                        "value": "WageModel"
                    }
                }
            }
        ]
    },
};
