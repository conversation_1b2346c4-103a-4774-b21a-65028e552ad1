export const expectedResponses = {
    QA_Component_GET_CLA: {
        year2025: {
            _embedded: [
                {
                    "balanceSheetSide": {
                        "key": 1,
                        "value": "Debet"
                    },
                    "baseForCalculationBter": null,
                    "category": {
                        "key": 26,
                        "value": "Output"
                    },
                    "column": {
                        "key": 1,
                        "value": "Loon in geld"
                    },
                    "costsEmployer": {
                        "key": 1,
                        "value": "+"
                    },
                    "deductionOrPayment": {
                        "key": 1,
                        "value": "Betaling"
                    },
                    "definedAtLevel": {
                        "balanceSheetSide": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "baseForCalculationBter": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "category": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "column": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "costsEmployer": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "deductionOrPayment": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "description": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "hoursIndication": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "id": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isBaseForCalculationDailyWageSupplement": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isBaseForCalculationDailyWageZw": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isBaseForCalculationOvertime": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isFullTime": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isNetToGross": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isOvertime": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isPayment": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isTravelExpense": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "paymentDescription": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "paymentPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "socialSecurityLiable": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "suppressPrinting": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "suppressPrintingAccumulations": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "taxLiable": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "description": "SUPPLETIE_CLA",
                    "hoursIndication": null,
                    "id": "0000095e-07e9-0064-0000-000000000000",
                    "inheritanceLevel": {
                        "id": "83e6ae60-7b64-4de2-9cd5-39f73aae7ad8",
                        "type": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "isBaseForCalculationDailyWageSupplement": false,
                    "isBaseForCalculationDailyWageZw": false,
                    "isBaseForCalculationOvertime": false,
                    "isFullTime": false,
                    "isNetToGross": false,
                    "isOvertime": false,
                    "isPayment": false,
                    "isTravelExpense": false,
                    "key": 100,
                    "paymentDescription": null,
                    "paymentPeriod": null,
                    "socialSecurityLiable": {
                        "key": 1,
                        "value": "+"
                    },
                    "suppressPrinting": false,
                    "suppressPrintingAccumulations": false,
                    "taxLiable": {
                        "key": 1,
                        "value": "Tabelloon +"
                    },
                    "year": 2025
                },
                {
                    "balanceSheetSide": {
                        "key": 1,
                        "value": "Debet"
                    },
                    "baseForCalculationBter": null,
                    "category": {
                        "key": 30,
                        "value": "Bedrag per eenheid"
                    },
                    "column": {
                        "key": 13,
                        "value": "Netto betalingen"
                    },
                    "costsEmployer": {
                        "key": 1,
                        "value": "+"
                    },
                    "deductionOrPayment": {
                        "key": 1,
                        "value": "Betaling"
                    },
                    "definedAtLevel": {
                        "balanceSheetSide": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "baseForCalculationBter": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "category": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "column": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "costsEmployer": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "deductionOrPayment": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "description": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "hoursIndication": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "id": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isBaseForCalculationDailyWageSupplement": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isBaseForCalculationDailyWageZw": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isBaseForCalculationOvertime": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isFullTime": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isNetToGross": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isOvertime": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isPayment": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isTravelExpense": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "paymentDescription": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "paymentPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "socialSecurityLiable": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "suppressPrinting": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "suppressPrintingAccumulations": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "taxLiable": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "description": "KM.VERGOED_CLA",
                    "hoursIndication": null,
                    "id": "0000095e-07e9-0065-0000-000000000000",
                    "inheritanceLevel": {
                        "id": "83e6ae60-7b64-4de2-9cd5-39f73aae7ad8",
                        "type": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "isBaseForCalculationDailyWageSupplement": false,
                    "isBaseForCalculationDailyWageZw": false,
                    "isBaseForCalculationOvertime": false,
                    "isFullTime": false,
                    "isNetToGross": false,
                    "isOvertime": false,
                    "isPayment": false,
                    "isTravelExpense": true,
                    "key": 101,
                    "paymentDescription": null,
                    "paymentPeriod": {
                        "key": 5,
                        "value": "Eenheid"
                    },
                    "socialSecurityLiable": null,
                    "suppressPrinting": false,
                    "suppressPrintingAccumulations": false,
                    "taxLiable": null,
                    "year": 2025
                },
                {
                    "balanceSheetSide": {
                        "key": 1,
                        "value": "Debet"
                    },
                    "baseForCalculationBter": null,
                    "category": {
                        "key": 30,
                        "value": "Bedrag per eenheid"
                    },
                    "column": {
                        "key": 1,
                        "value": "Loon in geld"
                    },
                    "costsEmployer": {
                        "key": 1,
                        "value": "+"
                    },
                    "deductionOrPayment": {
                        "key": 1,
                        "value": "Betaling"
                    },
                    "definedAtLevel": {
                        "balanceSheetSide": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "baseForCalculationBter": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "category": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "column": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "costsEmployer": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "deductionOrPayment": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "description": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "hoursIndication": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "id": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isBaseForCalculationDailyWageSupplement": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isBaseForCalculationDailyWageZw": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isBaseForCalculationOvertime": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isFullTime": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isNetToGross": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isOvertime": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isPayment": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "isTravelExpense": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "paymentDescription": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "paymentPeriod": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "socialSecurityLiable": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "suppressPrinting": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "suppressPrintingAccumulations": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "taxLiable": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "description": "KM.VERGOED_CLA",
                    "hoursIndication": null,
                    "id": "0000095e-07e9-0066-0000-000000000000",
                    "inheritanceLevel": {
                        "id": "83e6ae60-7b64-4de2-9cd5-39f73aae7ad8",
                        "type": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "isBaseForCalculationDailyWageSupplement": false,
                    "isBaseForCalculationDailyWageZw": false,
                    "isBaseForCalculationOvertime": false,
                    "isFullTime": false,
                    "isNetToGross": false,
                    "isOvertime": false,
                    "isPayment": false,
                    "isTravelExpense": false,
                    "key": 102,
                    "paymentDescription": null,
                    "paymentPeriod": {
                        "key": 5,
                        "value": "Eenheid"
                    },
                    "socialSecurityLiable": {
                        "key": 1,
                        "value": "+"
                    },
                    "suppressPrinting": false,
                    "suppressPrintingAccumulations": false,
                    "taxLiable": {
                        "key": 3,
                        "value": "Tariefloon +"
                    },
                    "year": 2025
                }
            ]
        },
    }
};