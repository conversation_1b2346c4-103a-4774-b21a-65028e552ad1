import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./Year.data";
import * as endpointData from "./GetYearMetadataByWageModelYearId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "wagemodels/years/{yearId}/metadata",
    method: "GET",
    params: {
        yearId: entityData.yearIds.QA_Year_GET_WM.year2025,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        "GetYearMetadataByWageModelYearId should return 200, the correct resourceVersion+obsoleteDate and the correct Year metadata at wage model level to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_Year_GET_WM.year2025,
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "content",
                        value: endpointData.expectedResponses.QA_Year_GET_WM.year2025.content,
                        mode: ValidationMode.strict,
                    },
                ],
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        "GetYearMetadataByWageModelYearId should return 403 when yearId=non-yearGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetYearMetadataByWageModelYearId should return 403 when yearId=collectiveLaborAgreementYearId (blocked level)": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_Year_GET_CLA.year2025
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.blockedInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetYearMetadataByWageModelYearId should return 403 when yearId=payrollAdministrationYearId (blocked level)": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_Year_GET_PA.year2025
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.blockedInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
