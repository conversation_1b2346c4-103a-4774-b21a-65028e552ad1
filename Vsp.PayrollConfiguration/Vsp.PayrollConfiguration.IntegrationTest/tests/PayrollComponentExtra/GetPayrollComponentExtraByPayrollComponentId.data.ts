export const expectedResponses = {
    "content": {
        "definedAtLevel": {
            "deviatingDescription": {
                "key": 1,
                "value": "CollectiveLaborAgreement"
            },
            "routeType": {
                "key": 1,
                "value": "CollectiveLaborAgreement"
            }
        },
        "deviatingDescription": "QA_ComponentDeviantDescription_CLA",
        "routeType": {
            "key": 2,
            "value": "Kortste"
        }
    },
    "messages": [],
    "version": {
        "obsoleteDate": null,
        "resourceVersion": "2018-01-01"
    }
}