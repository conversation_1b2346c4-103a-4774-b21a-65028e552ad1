import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./UnitPercentage.data";
import * as endpointData from "./GetUnitPercentageMetadataByYearId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "unitpercentages/metadata/years/{yearId}",
    params: {
        yearId: entityData.yearIds.QA_UnitPercentage_GET_CLA.year2025,
    },
    method: "GET",
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        "GetUnitPercentagesMetadataByYearId (CLA) should return 200, the correct resourceVersionersion+obsoleteDate and the correct year metadata (for Unit Percentage on collective labor agreement level) to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_UnitPercentage_GET_CLA.year2025,
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "content",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration_CLA.content,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        "GetUnitPercentagesMetadataByYearId (WM) should return 200 and the correct year metadata (for Unit Percentage on wage model level)": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_UnitPercentage_GET_WM.year2025,
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "content",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration_WM.content,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        "GetUnitPercentagesMetadataByYearId (PA) should return 200 and the correct year metadata (for Unit Percentage on payroll configuration level)": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_UnitPercentage_GET_PA.year2025,
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "content",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration_PA.content,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetUnitPercentagesMetadataByYearId should return 403 when yearId=non-yearGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
