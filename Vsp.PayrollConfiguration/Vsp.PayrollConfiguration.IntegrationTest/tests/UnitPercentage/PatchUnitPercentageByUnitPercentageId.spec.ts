import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./UnitPercentage.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "unitpercentages/{unitPercentageId}",
    method: "PATCH",
    params: {
        unitPercentageId: entityData.unitPercentageIds.QA_UnitPercentage_PATCH_PA_ForLevel_PA.Year2025Component42Period1,
    },
    modules: {
        methodNotAllowed: ["GET", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,     // Covered in C# integration tests
        token: "QA_PayrollConfiguration1",
        body: {
            "percentage": 11.995,
            "calculateOver": {
                "key": 1
            }
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "PatchUnitPercentageByUnitPercentageId should return 400 and the correct message for invalid calculateOver value": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    unitPercentageId: entityData.unitPercentageIds.QA_UnitPercentage_PATCH_PA_ForLevel_PA.Year2025Component42Period1,
                },
                body: {
                    "percentage": 89.123,
                    "calculateOver": {
                        "key": 3
                    },
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_UnitPercentage_CalculateOver_Invalid",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PatchUnitPercentageByUnitPercentageId should return 403 when unitPercentageId=non-unitPercentageId": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    unitPercentageId: sharedData.nonEntityGuid,
                },
                body: {
                    "percentage": 89.123,
                    "calculateOver": {
                        "key": 0
                    }
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
