import {testEndpoint, ITestConfig, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculation.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "basesforcalculation/{baseForCalculationId}",
    method: "DELETE",
    params: {
        baseForCalculationId: entityData.baseForCalculationIds.QA_BaseForCalculation_DELETE_CLA.year2025.baseForCalculation_1,
    },
    modules: {
        methodNotAllowed: ["GET", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        "DeleteBaseForCalculationByBaseForCalculationId should return 400 and EntityHasChildren message": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: entityData.baseForCalculationIds.QA_BaseForCalculation_DELETE_CLA.year2025.baseForCalculation_1,
                },
                timeout: 120000,
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 1,
                        mode: ValidationMode.count
                    },
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_Delete_EntityHasChildren",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        "DeleteBaseForCalculationByBaseForCalculationId should return 400 and HasChild_BasePayrollComponent message": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: entityData.baseForCalculationIds.QA_BaseForCalculation_DELETE_PA.year2025.baseForCalculation_3,
                },
                timeout: 120000,
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 1,
                        mode: ValidationMode.count
                    },
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_BaseForCalculation_Delete_HasChild_BasePayrollComponent",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "DeleteBaseForCalculationByBaseForCalculationId should return 403 when baseForCalculationId=non-baseForCalculationGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
