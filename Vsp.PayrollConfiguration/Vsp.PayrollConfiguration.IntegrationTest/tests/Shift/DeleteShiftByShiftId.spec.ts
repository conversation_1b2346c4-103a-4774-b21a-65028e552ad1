import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./Shift.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "shifts/{shiftId}",
    params: {
        shiftId: entityData.shiftIds.QA_PayrollConfiguration_CLA_ForLevel_CLA.shift01,
    },
    method: "DELETE",
    modules: {
        methodNotAllowed: ["GET", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "DeleteShiftByShiftId should return 403 when shiftId=non-shiftId": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    shiftId: sharedData.nonEntityGuid,
                },
                queryStringParams: {}
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
