FROM cypress/base:latest AS build
ARG NPM_TOKEN

WORKDIR /app
COPY ./.npmrc ./.npmrc
COPY ./package.json ./package.json

RUN npm install
RUN rm -rf ./.npmrc

FROM cypress/base:latest

WORKDIR /app
COPY --from=build /app /app

RUN npm install cypress

ENV serviceUrl http://localhost:4200
ENV authUrl http://localhost:4200

COPY ./tsconfig.json ./tsconfig.json
COPY ./cypress.json ./cypress.json
COPY ./tests ./tests
COPY ./fixtures ./fixtures
COPY ./scripts ./scripts

CMD npm run cypress:run -- --env serviceUrl=${serviceUrl},authUrl=${authUrl}
