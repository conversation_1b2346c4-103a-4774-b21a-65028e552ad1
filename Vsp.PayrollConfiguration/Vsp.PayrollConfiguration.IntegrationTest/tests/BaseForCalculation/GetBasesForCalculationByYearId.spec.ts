import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculation.data";
import * as endpointData from "./GetBasesForCalculationByYearId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "years/{yearId}/basesforcalculation",
    method: "GET",
    params: {
        yearId: entityData.yearIds.QA_BaseForCalculation_GET_CLA.year2025,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        "GetBasesForCalculationsByYearId (CLA) should return 200, the correct list of bases for calculation for 2025 at collective labor agreement level to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_BaseForCalculation_GET_CLA.year2025,
                },
                queryStringParams: {
                    pageSize: "50",
                    orderBy: "key",
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_BaseForCalculation_GET_CLA.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_BaseForCalculation_GET_CLA.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_BaseForCalculation_GET_CLA.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        // request with x-reportinput not necessary, covered in C# integration tests
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetBasesForCalculationsByYearId should return 403 when yearId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});