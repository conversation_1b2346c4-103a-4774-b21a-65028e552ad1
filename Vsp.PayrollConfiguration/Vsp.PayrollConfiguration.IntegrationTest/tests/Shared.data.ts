import { EndpointType } from "@vsp/qa-api-testing/dist";
import * as env from "../scripts/Environment";

export const sharedCurrentDate = new Date().toISOString().slice(0, 10);
export const sharedCurrentDateTime = new Date().toISOString().slice(0, 10) + " " + new Date().getHours() + ":" + new Date().getMinutes() + ":" + new Date().getSeconds();

export interface IBaseTestConfig {
    serviceUrl: string;
    authServiceUrl?: string;
    personServiceUrl?: string;
    globalFilterUrl: string,
    endpointType: EndpointType.microService,
};

export const baseConfig: IBaseTestConfig = {
    serviceUrl: env.getServiceUrl(),
    authServiceUrl: env.getAuthUrl(),
    personServiceUrl: env.getPersonUrl(),
    globalFilterUrl: env.getGlobalFilterUrl(),
    endpointType: EndpointType.microService,
};

export const zeroGuid = "00000000-0000-0000-0000-000000000000";
export const nonEntityGuid = "17010026-1701-0026-1701-002617010026";

export const requestHeaders = {
    "content-type": "application/json"
};

export const providerIds = {
    QA_PayrollConfiguration1: "db6ac336-bcc3-42b7-b40a-b0d25f66c83f",
    QA_PayrollConfiguration2: "8d20dffc-49b6-454c-93e1-574d6a4dcb06"
}

export const users = {
    // Provider: QA_AKPayrollConfiguration1
    QA_PayrollConfiguration1: { // Provider user
        "grant_type": "password",
        "client_id": "Loket3",
        "username": "QA_PayrollConfiguration1",
        "password": "L0k3tQ@!"
    },
    QA_AKAdministrationFilter: { // access to all teams and employers, no default filter
        "grant_type": "password",
        "client_id": "Loket3",
        "username": "QA_AKAdministrationFilter",
        "password": "L0k3tQ@!",
    },
    QA_AdministrationFilter_Year_User: { // only acces to QA_AdministrationFilter_Year, with 3 administrations starting at 2019, 2020 and 2025
        "grant_type": "password",
        "client_id": "Loket3",
        "username": "QA_AdministrationFilter_Year_User",
        "password": "L0k3tQ@!",
    },
};

export const versions = {
    v20180101: {
        resourceVersion: "2018-01-01",
        obsoleteDate: null,
    },
};

export const defaultErrors = {
    // These errors are specific to inheritance entities:
    unknownInheritanceLevel: { "message": "Insufficient rights for this request, reason: Unknown inheritance level." },
    blockedInheritanceLevel: { "message": "Insufficient rights for this request, reason: Blocked inheritance level." },
    // These errors are general - copied from API gateway Cypress project:
    noParentIdNonEmployerGuid: { "message": "Insufficient rights for this request, reason: \"Path to werkgever not found for entity with Id: 17010026-1701-0026-1701-002617010026\"" },
    noParentIdNullEmployerGuid: { "message": "Insufficient rights for this request, reason: \"Path to werkgever not found for entity with Id: 00000000-0000-0000-0000-000000000000\"" },
    noParentIdNonInheritanceGuid: { "message": "Insufficient rights for this request, reason: \"Path to inheritance parent not found for entity with Id: 17010026-1701-0026-1701-002617010026\"" },
    noParentIdNullInheritanceGuid: { "message": "Insufficient rights for this request, reason: \"Path to inheritance parent not found for entity with Id: 00000000-0000-0000-0000-000000000000\"" },
    noParentIdNonProviderGuid: { "message": "Insufficient rights for this request, reason: \"Path to provider not found for entity with Id: 17010026-1701-0026-1701-002617010026\"" },
    noParentIdNullProviderGuid: { "message": "Insufficient rights for this request, reason: \"Path to provider not found for entity with Id: 00000000-0000-0000-0000-000000000000\"" },
    noParentIdNonGebruikerGuid: { "message": "Insufficient rights for this request, reason: \"Path to gebruiker not found for entity with Id: 17010026-1701-0026-1701-002617010026\"" },
    noParentIdNullGebruikerGuid: { "message": "Insufficient rights for this request, reason: \"Path to gebruiker not found for entity with Id: 00000000-0000-0000-0000-000000000000\"" },
};
