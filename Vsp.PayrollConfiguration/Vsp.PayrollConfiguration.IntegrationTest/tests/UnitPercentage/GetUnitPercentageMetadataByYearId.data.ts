export const expectedResponses = {
    QA_PayrollConfiguration_CLA: {
        content: {
            "payrollComponent": [
                {
                    "key": 41,
                    "description": "CLA UNIT 41",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 42,
                    "description": "CLA UNIT 42",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 43,
                    "description": "CLA UNIT 43",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 44,
                    "description": "CLA UNIT 44",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                }
            ],
            "calculateOver": [
                {
                    "key": 0,
                    "value": "Bedrag per eenheid"
                },
                {
                    "key": 1,
                    "value": "Periode uurloon"
                },
                {
                    "key": 2,
                    "value": "Gemiddeld uurloon"
                }
            ]
        }
    },
    QA_PayrollConfiguration_WM: {
        content: {
            "payrollComponent": [
                {
                    "key": 41,
                    "description": "CLA UNIT 41",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 42,
                    "description": "CLA UNIT 42",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 43,
                    "description": "WM UNIT 43",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 44,
                    "description": "CLA UNIT 44",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 53,
                    "description": "WM UNIT 53",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                }
            ],
            "calculateOver": [
                {
                    "key": 0,
                    "value": "Bedrag per eenheid"
                },
                {
                    "key": 1,
                    "value": "Periode uurloon"
                },
                {
                    "key": 2,
                    "value": "Gemiddeld uurloon"
                }
            ]
        }
    },
    QA_PayrollConfiguration_PA: {
        content: {
            "payrollComponent": [
                {
                    "key": 41,
                    "description": "CLA UNIT 41",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 42,
                    "description": "CLA UNIT 42",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 43,
                    "description": "WM UNIT 43",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 44,
                    "description": "PA UNIT 44",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 53,
                    "description": "WM UNIT 53",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                },
                {
                    "key": 58,
                    "description": "PA UNIT 58",
                    "category": {
                        "key": 6,
                        "value": "Eenheden"
                    }
                }
            ],
            "calculateOver": [
                {
                    "key": 0,
                    "value": "Bedrag per eenheid"
                },
                {
                    "key": 1,
                    "value": "Periode uurloon"
                },
                {
                    "key": 2,
                    "value": "Gemiddeld uurloon"
                }
            ]
        }
    },
};