export const expectedResponses = {
    QA_Year_GET_WM: {
        year2025: {
            content: {
                "aof": [
                    {
                        "key": 1,
                        "value": "Kleine werkgever"
                    },
                    {
                        "key": 2,
                        "value": "(Middel) grote werkgever"
                    }
                ],
                "standardShift": [
                    {
                        "shiftNumber": 1,
                        "fullTimeHoursPerWeek": 40.25,
                        "bonusPercentage": 0.025
                    },
                    {
                        "shiftNumber": 2,
                        "fullTimeHoursPerWeek": 38.25,
                        "bonusPercentage": 5.025
                    },
                    {
                        "shiftNumber": 3,
                        "fullTimeHoursPerWeek": 36.21,
                        "bonusPercentage": 10.021
                    },
                    {
                        "shiftNumber": 10,
                        "fullTimeHoursPerWeek": 40.25,
                        "bonusPercentage": 0.025
                    },
                    {
                        "shiftNumber": 11,
                        "fullTimeHoursPerWeek": 38.25,
                        "bonusPercentage": 5.025
                    },
                    {
                        "shiftNumber": 12,
                        "fullTimeHoursPerWeek": 36.25,
                        "bonusPercentage": 10.025
                    },
                    {
                        "shiftNumber": 15,
                        "fullTimeHoursPerWeek": 17.25,
                        "bonusPercentage": 17.025
                    }
                ],
                "standardEmployeeProfile": [
                    {
                        "employeeProfileNumber": 1,
                        "description": "WM employee profile 2025"
                    },
                    {
                        "employeeProfileNumber": 2,
                        "description": "PA employee profile 2021"
                    }
                ]
            }
        },
    },
};
