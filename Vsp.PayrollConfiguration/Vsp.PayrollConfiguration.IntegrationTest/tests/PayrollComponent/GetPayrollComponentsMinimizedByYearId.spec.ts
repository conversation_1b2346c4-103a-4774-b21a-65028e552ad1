import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./PayrollComponent.data";
import * as endpointData from "./GetPayrollComponentsMinimizedByYearId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "years/{yearId}/payrollcomponents/minimized",
    method: "GET",
    params: {
        yearId: entityData.yearIds.QA_PayrollComponent_GET_CLA.year2025,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        "GetPayrollComponentsMinimizedByYearId (CLA) should return 200, the correct resourceVersion+obsoleteDate, and the correct list of Minimized Components for 2025 at collective labor agreement level to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_PayrollComponent_GET_CLA.year2025,
                },
                queryStringParams: {
                    pageSize: "250",
                    orderBy: "key",
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_MinimizedComponents_GET_CLA.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_MinimizedComponents_GET_CLA.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_MinimizedComponents_GET_CLA.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        // ---403---
        "GetPayrollComponentsMinimizedByYearId should return 403 when yearId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});