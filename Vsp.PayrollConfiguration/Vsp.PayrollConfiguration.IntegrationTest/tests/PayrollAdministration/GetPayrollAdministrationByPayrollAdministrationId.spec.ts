import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./PayrollAdministration.data";
import * as endpointData from "./GetPayrollAdministrationByPayrollAdministrationId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrolladministrations/{payrollAdministrationId}",
    params: {
        payrollAdministrationId: entityData.payrollAdministrationIds.QA_PayrollProcessOverview.QA_PPOMaand,
    },
    method: "GET",
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_AKPayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        "GetPayrollAdministrationByPayrollAdministrationId should return 200, the correct resourceVersion+obsoleteDate and the correct details of Payroll Administration QA_PayrollConfiguration1 to provider user QA_AKPayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                params: {
                    payrollAdministrationId: entityData.payrollAdministrationIds.QA_PayrollConfiguration1.QA_PayrollConfiguration1,
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "content",
                        value: endpointData.expectedResponses.QA_PayrollAdministration1.content,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetUlsaPayrollAdministrationByPayrollAdministrationId should return 403 when payrollAdministrationId=non-payrollAdministrationGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                params: {
                    payrollAdministrationId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonInheritanceGuid,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
