import {testEndpoint, ITestConfig, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./PayrollComponentExtra.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrollcomponents/{payrollComponentId}/extra",
    method: "PATCH",
    params: {
        payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponentExtra_PATCH_PA.year2025.component100,
    },
    modules: {
        methodNotAllowed: ["DELETE", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,      // Covered in C# integration tests
        body: {
            "deviatingDescription": "test",
            "routeType": { "key": 1 },
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "PatchPayrollComponentExtraByPayrollComponentId should return 400 and the correct message for non-unit component": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponentExtra_PATCH_PA.year2025.component100,
                },
                body: {
                    "deviatingDescription": "test",
                    "routeType": { "key": 1 },
                },
                timeout: 120000,
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 1,
                        mode: ValidationMode.count
                    },
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_PayrollComponentExtra_RouteType_NotApplicable",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PatchPayrollComponentExtraByPayrollComponentId should return 403 when payrollComponentId=non-payrollComponentGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: sharedData.nonEntityGuid,
                },
                body: {
                    "deviatingDescription": "test",
                    "routeType": { "key": 1 },
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
