export const expectedResponses = {
    QA_PayrollConfiguration_CLA_ForLevel_CLA: {
        year2025: {
            _embedded: [
                {
                    "bonusPercentage": 11.25,
                    "definedAtLevel": {
                        "bonusPercentage": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "fullTimeHoursPerWeek": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "id": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "fullTimeHoursPerWeek": 11.25,
                    "id": "0000094c-07e9-0001-0100-000000000000",
                    "inheritanceLevel": {
                        "id": "1c54c3ea-c8a0-4489-81f9-9b6c63380f4a",
                        "type": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "shiftNumber": 1,
                    "startPayrollPeriod": {
                        "payrollPeriodId": 202501,
                        "periodEndDate": "2025-01-31",
                        "periodNumber": 1,
                        "periodStartDate": "2025-01-01",
                        "year": 2025
                    },
                    "year": 2025
                }
            ]
        }
    },
};