export const expectedResponses = {
    QA_AbpFund_GET_CLA: {
        year2025: {
            _embedded: [
                {
                    "definedAtLevel": {
                        "employmentContribution": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "franchise": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "franchiseUpToAge40": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "franchiseUpToAge50": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "id": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "totalContribution": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "description": "ABP Pensioen/NP (Vanaf 2018 excl ANW)",
                    "employmentContribution": 30,
                    "franchise": 5,
                    "franchiseUpToAge40": 6,
                    "franchiseUpToAge50": 7,
                    "id": "000009e5-07e9-0001-0200-000000000000",
                    "inheritanceLevel": {
                        "id": "d7e057bf-9f6c-45a8-8703-b94c2f50f979",
                        "type": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "key": 1,
                    "startPayrollPeriod": {
                        "payrollPeriodId": 202502,
                        "periodEndDate": "2025-02-28",
                        "periodNumber": 2,
                        "periodStartDate": "2025-02-01",
                        "year": 2025
                    },
                    "totalContribution": 30,
                    "year": 2025
                },
                {
                    "definedAtLevel": {
                        "employmentContribution": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "franchise": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "franchiseUpToAge40": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "franchiseUpToAge50": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "id": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "totalContribution": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "description": "ABP Pensioen/NP (Wachtgeld)",
                    "employmentContribution": 10,
                    "franchise": 12,
                    "franchiseUpToAge40": 13,
                    "franchiseUpToAge50": 14,
                    "id": "000009e5-07e9-0002-0100-000000000000",
                    "inheritanceLevel": {
                        "id": "d7e057bf-9f6c-45a8-8703-b94c2f50f979",
                        "type": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "key": 2,
                    "startPayrollPeriod": {
                        "payrollPeriodId": 202501,
                        "periodEndDate": "2025-01-31",
                        "periodNumber": 1,
                        "periodStartDate": "2025-01-01",
                        "year": 2025
                    },
                    "totalContribution": 11,
                    "year": 2025
                },
                {
                    "definedAtLevel": {
                        "employmentContribution": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "franchise": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "franchiseUpToAge40": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "franchiseUpToAge50": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "id": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        },
                        "totalContribution": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "description": "AP",
                    "employmentContribution": 1.234,
                    "franchise": 20.25,
                    "franchiseUpToAge40": 20.4,
                    "franchiseUpToAge50": 20.5,
                    "id": "000009e5-07e9-0003-0100-000000000000",
                    "inheritanceLevel": {
                        "id": "d7e057bf-9f6c-45a8-8703-b94c2f50f979",
                        "type": {
                            "key": 1,
                            "value": "CollectiveLaborAgreement"
                        }
                    },
                    "key": 3,
                    "startPayrollPeriod": {
                        "payrollPeriodId": 202501,
                        "periodEndDate": "2025-01-31",
                        "periodNumber": 1,
                        "periodStartDate": "2025-01-01",
                        "year": 2025
                    },
                    "totalContribution": 4.321,
                    "year": 2025
                }
            ]
        }
    }
};