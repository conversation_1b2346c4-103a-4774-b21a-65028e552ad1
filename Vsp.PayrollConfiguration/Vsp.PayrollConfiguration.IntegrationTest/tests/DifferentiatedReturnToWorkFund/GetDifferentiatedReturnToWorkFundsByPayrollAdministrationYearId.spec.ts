import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./DifferentiatedReturnToWorkFund.data";
import * as endpointData from "./GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrolladministrations/years/{yearId}/differentiatedreturntoworkfunds",
    method: "GET",
    params: {
        yearId: entityData.yearIds.QA_DifferentiatedReturnToWorkFund_GET_PA.year2025,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        "GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearId should return 200, the correct resourceVersion+obsoleteDate and the correct list of Differentiated Return To Work Funds for 2025 at payroll administration level to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_DifferentiatedReturnToWorkFund_GET_PA.year2025,
                },
                queryStringParams: {
                    orderBy: "startPayrollPeriod.year,startPayrollPeriod.periodNumber"
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_DifferentiatedReturnToWorkFund_GET_PA.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_DifferentiatedReturnToWorkFund_GET_PA.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_DifferentiatedReturnToWorkFund_GET_PA.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        "GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearId with x-reportinput should return 200 and the correct report of DifferentiatedReturnToWorkFunds": {
            request: {
                headers: {
                    "accept": "text/csv",
                    'x-reportinput': '{"FileNameWithoutExtension":"DifferentiatedReturnToWorkFundsByPayrollAdministrationYear","Fields":[{"FieldName":"wga.totalContribution","ReportColumnName":"Afdracht% WGA"},{"FieldName":"wga.employmentContribution","ReportColumnName":"Premie% WGA"},{"FieldName":"zw.totalContribution","ReportColumnName":"Afdracht% ZW"},{"FieldName":"startPayrollPeriod.year","ReportColumnName":"Jaar"},{"FieldName":"startPayrollPeriod.periodNumber","ReportColumnName":"Periode"},{"FieldName":"id","ReportColumnName":"LoketID"}]}',
                    "content-type": "application/json",
                },
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_DifferentiatedReturnToWorkFund_GET_PA.year2025,
                },
                queryStringParams: {
                    orderBy: "startPayrollPeriod.year,startPayrollPeriod.periodNumber"
                },
            },
            response: {
                code: 200,
                headers: {
                    "content-disposition": new RegExp("attachment; filename=\"DifferentiatedReturnToWorkFundsByPayrollAdministrationYear " + sharedData.sharedCurrentDate + " ([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9].csv\""),
                    "content-type": "text/csv; charset=utf-8",
                },
                validations: [
                    {
                        value: "Afdracht% WGA;Premie% WGA;Afdracht% ZW;Jaar;Periode;LoketID\r\n2,000;1,000;3,000;2025;1;bf3c804c-e064-453c-99a0-a1cee506ae99\r\n2,200;1,200;3,200;2025;2;452a02d2-ed1b-4807-8d36-cc58ba8cc0dd\r\n3,320;1,320;3,320;2025;3;55d93032-b51d-4dc7-94aa-b20115f8ed01\r\n2,432;1,432;3,432;2025;4;808a618b-cad4-41f4-96f4-27f385f3d969\r\n0,000;0,000;0,000;2025;5;376f66ae-4a7b-4110-b4aa-963efc705567\r\n",
                        mode: ValidationMode.strict,
                    },
                ]
            }
        },
        // request with x-reportinput not necessary, covered in C# integration tests
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearId should return 403 when yearId=non-yearGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearId should return 403 when yearId=collectiveLaborAgreementYearId (blocked level)": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_PayrollConfiguration1_CLA_ForLevel_CLA.year2025
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.blockedInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetDifferentiatedReturnToWorkFundsByPayrollAdministrationYearId should return 403 when yearId=wageModelYearId (blocked level)": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_PayrollConfiguration1_WM_ForLevel_WM.year2025
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.blockedInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
