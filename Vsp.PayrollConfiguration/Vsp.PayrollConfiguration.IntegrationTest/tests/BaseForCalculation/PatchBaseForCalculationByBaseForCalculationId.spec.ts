import {ITestConfig, testEndpoint, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculation.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "basesforcalculation/{baseForCalculationId}",
    method: "PATCH",
    params: {
        baseForCalculationId: entityData.baseForCalculationIds.QA_BaseForCalculation_PATCH_CLA.year2025.baseForCalculation_25,
    },
    modules: {
        methodNotAllowed: ["GET", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,     // Covered in C# integration tests
        token: "QA_PayrollConfiguration1",
        body: {
            "description": "CYPRESS_DONT_TOUCH",
            "baseType": null,
            "startEmployeeAgeType": null,
            "startEmployeeAge": 0.0,
            "endEmployeeAgeType": null,
            "endEmployeeAge": 0.0,
            "resultPayrollComponent": { "key": 3150 },
            "percentage": 0.0,
            "calculationPayrollPeriod": { "periodNumber": 1 },
            "referencePayrollPeriod": null,
            "payoutPayrollPeriod": { "periodNumber": 1 },
            "accrualEndPayrollPeriod": null,
            "payslipType": { "key": 4 },
            "isPayoutAtStartOfEmployment": false,
            "isPayoutAtEndOfEmployment": false,
            "advancePayrollComponent": null,
            "advancePercentage": 0.0,
            "advancePayrollPeriod": null,
            "periodicReservationPayrollComponent": null,
            "financialReservationPercentage": 0.0,
            "financialMarkupPercentage": 0.0,
            "isCumulativeCalculation": true,
            "isPartTimeCalculation": false,
            "isAutomaticCalculation": false,
            "isSupplementingDailyWage": false,
            "minimumMaximumType": { "key": 2 }
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "PatchBaseForCalculationByBaseForCalculationId should return 400 and the correct message for invalid PayoutPayrollPeriod value": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: entityData.baseForCalculationIds.QA_BaseForCalculation_PATCH_CLA.year2025.baseForCalculation_25,
                },
                timeout: 120000,
                body: {
                    "description": "CYPRESS_DONT_TOUCH",
                    "baseType": null,
                    "startEmployeeAgeType": null,
                    "startEmployeeAge": 0.0,
                    "endEmployeeAgeType": null,
                    "endEmployeeAge": 0.0,
                    "resultPayrollComponent": { "key": 3150 },
                    "percentage": 0.0,
                    "calculationPayrollPeriod": { "periodNumber": 1 },
                    "referencePayrollPeriod": null,
                    "payoutPayrollPeriod": null, // Should trigger rule #9 API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_2
                    "accrualEndPayrollPeriod": null,
                    "payslipType": { "key": 4 },
                    "isPayoutAtStartOfEmployment": false,
                    "isPayoutAtEndOfEmployment": false,
                    "advancePayrollComponent": null,
                    "advancePercentage": 0.0,
                    "advancePayrollPeriod": null,
                    "periodicReservationPayrollComponent": null,
                    "financialReservationPercentage": 0.0,
                    "financialMarkupPercentage": 0.0,
                    "isCumulativeCalculation": true,
                    "isPartTimeCalculation": false,
                    "isAutomaticCalculation": false,
                    "isSupplementingDailyWage": false,
                    "minimumMaximumType": { "key": 2 }
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_BaseForCalculation_CalculationPayrollPeriod_2",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PatchBaseForCalculationByBaseForCalculationId should return 403 when baseForCalculationId=non-baseForCalculationId": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: sharedData.nonEntityGuid,
                },
                body: {
                    "description": "CYPRESS_DONT_TOUCH",
                    "baseType": null,
                    "startEmployeeAgeType": null,
                    "startEmployeeAge": 0.0,
                    "endEmployeeAgeType": null,
                    "endEmployeeAge": 0.0,
                    "resultPayrollComponent": { "key": 3150 },
                    "percentage": 0.0,
                    "calculationPayrollPeriod": { "periodNumber": 1 },
                    "referencePayrollPeriod": null,
                    "payoutPayrollPeriod": { "periodNumber": 1 },
                    "accrualEndPayrollPeriod": null,
                    "payslipType": { "key": 4 },
                    "isPayoutAtStartOfEmployment": false,
                    "isPayoutAtEndOfEmployment": false,
                    "advancePayrollComponent": null,
                    "advancePercentage": 0.0,
                    "advancePayrollPeriod": null,
                    "periodicReservationPayrollComponent": null,
                    "financialReservationPercentage": 0.0,
                    "financialMarkupPercentage": 0.0,
                    "isCumulativeCalculation": true,
                    "isPartTimeCalculation": false,
                    "isAutomaticCalculation": false,
                    "isSupplementingDailyWage": false,
                    "minimumMaximumType": { "key": 2 }
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
