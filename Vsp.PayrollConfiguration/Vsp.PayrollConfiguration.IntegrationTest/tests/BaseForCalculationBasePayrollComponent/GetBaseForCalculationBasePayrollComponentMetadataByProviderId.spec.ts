import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "providers/{providerId}/basesforcalculation/basepayrollcomponents/metadata",
    method: "GET",
    params: {
        providerId: sharedData.providerIds.QA_PayrollConfiguration1,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        "GetBaseForCalculationBasePayrollComponentMetadataByProviderId should return 200, the correct resourceVersion+obsoleteDate, and the correct list of provider metadata (for BaseForCalculationBasePayrollComponent) to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    providerId: sharedData.providerIds.QA_PayrollConfiguration1,
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "content.origin",
                        mode: ValidationMode.nonEmpty,
                    }
                ],
            }
        },
        // ---403---
        "GetBaseForCalculationBasePayrollComponentMetadataByProviderId should return 403 when providerId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    providerId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonProviderGuid,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});