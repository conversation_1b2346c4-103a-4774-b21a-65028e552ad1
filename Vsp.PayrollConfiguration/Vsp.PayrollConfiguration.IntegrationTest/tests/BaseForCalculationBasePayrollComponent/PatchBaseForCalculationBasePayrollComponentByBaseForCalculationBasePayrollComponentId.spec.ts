import {ITestConfig, testEndpoint, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculationBasePayrollComponent.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "basesforcalculation/basepayrollcomponents/{baseForCalculationBasePayrollComponentId}",
    method: "PATCH",
    params: {
        baseForCalculationBasePayrollComponentId: entityData.baseForCalculationBasePayrollComponentIds.QA_BFC_BasePayrollComponent_PATCH_CLA_2025.year2025.baseForCalculation_1.basePayrollComponent_1025,
    },
    modules: {
        methodNotAllowed: ["GET", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,
        token: "QA_PayrollConfiguration1",
        body: {
            "origin": { "key": 0 }
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "PatchBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentId should return 400 and the correct message for invalid Origin value": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationBasePayrollComponentId: entityData.baseForCalculationBasePayrollComponentIds.QA_BFC_BasePayrollComponent_PATCH_CLA_2025.year2025.baseForCalculation_1.basePayrollComponent_1025,
                },
                timeout: 120000,
                body: {
                    "origin": { "key": 5 }
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_BaseForCalculationBasePayrollComponent_Origin_Invalid",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PatchBaseForCalculationBasePayrollComponentByBaseForCalculationBasePayrollComponentId should return 403 when baseForCalculationBasePayrollComponentId=non-baseForCalculationBasePayrollComponentId": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationBasePayrollComponentId: sharedData.nonEntityGuid,
                },
                body: {
                    "origin": { "key": 1 }
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
