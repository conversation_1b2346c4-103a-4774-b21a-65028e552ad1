import {ITestConfig, testEndpoint, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./PayrollComponentExtra.data";
import * as endpointData from "./GetPayrollComponentExtraByPayrollComponentId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrollcomponents/{payrollComponentId}/extra",
    method: "GET",
    params: {
        payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponentExtra_GET_CLA.year2025.component50,
    },
    modules: {
        methodNotAllowed: ["DELETE", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        "GetPayrollComponentExtraByPayrollComponentId should return 200, the correct Component Deviating for 2025 at collective labor agreement level to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponentExtra_GET_CLA.year2025.component50,
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "content",
                        value: endpointData.expectedResponses.content,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetPayrollComponentExtraByPayrollComponentId should return 403 when payrollComponentId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
