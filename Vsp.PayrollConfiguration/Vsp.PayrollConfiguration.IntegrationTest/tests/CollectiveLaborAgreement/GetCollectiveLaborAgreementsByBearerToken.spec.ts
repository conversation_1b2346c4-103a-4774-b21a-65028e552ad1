import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as endpointData from "./GetCollectiveLaborAgreementsByBearerToken.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "collectivelaboragreements",
    method: "GET",
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_AKPayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        "GetCollectiveLaborAgreementsByBearerToken should return 200, the correcte resourceVersion+obsoleteDate and the correct list of Collective Labor Agreements to provider user QA_AKPayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                params: {
                },
                queryStringParams: {
                    filter: "description lk 'QA_PayrollConfiguration'",
                    orderBy: "description",
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: 5,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_AKPayrollConfiguration1._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_AKPayrollConfiguration1._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        }
        // request with x-reportinput not necessary, covered in C# integration tests
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
