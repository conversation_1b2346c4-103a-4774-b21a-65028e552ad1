export const collectiveLaborAgreementIds = {
    QA_PayrollComponent_GET_CLA: "83e6ae60-7b64-4de2-9cd5-39f73aae7ad8",
    QA_PayrollComponent_POST_CLA: "654fc3b7-5eb3-47c2-a285-ec5a888669bf",
};

export const wageModelIds = {
    QA_PayrollComponent_GET_WM: "8c27baf0-9e27-4d14-8e67-de50659ef06b",
    QA_PayrollComponent_POST_WM: "c8c71e3f-27e4-4b6b-abde-63254f24cc53",
};

export const payrollAdministrationIds = {
    QA_PayrollComponent_GET_PA: "82ac85be-082b-4b72-b719-5b47098a122b",
    QA_PayrollComponent_POST_PA: "c6415e06-d1ed-4a75-86ba-5415eea6c9fa",
};

export const yearIds = {
    QA_PayrollComponent_GET_CLA: {
        year2025: "0000095e-07e9-0000-0000-000000000000"
    },
    QA_PayrollComponent_GET_WM: {
        year2025: "00000960-07e9-0000-0000-000000000000"
    },
    QA_PayrollComponent_GET_PA: {
        year2025: "00000961-07e9-0000-0000-000000000000"
    },
};

export const payrollComponentIds = {
    QA_PayrollComponent_PATCH_CLA: {
        year2025: {
            component_673: "00000985-07e9-02a1-0000-000000000000",
        }
    },
    QA_PayrollComponent_DELETE_CLA: {
        year2025: {
            component_60: "00000991-07e9-003c-0000-000000000000",
        }
    },
}
