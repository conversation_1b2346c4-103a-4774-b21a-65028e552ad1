export const expectedResponses = {
    QA_AKPayrollConfiguration1: {
        _embedded: [
            {
                "collectiveLaborAgreement": {
                    "comment": "<br>",
                    "description": "QA_PayrollConfiguration_4Week_CLA",
                    "id": "1aaecb1e-f8e4-4854-a314-c863b88215ca"
                },
                "comment": "<br>",
                "description": "QA_PayrollConfiguration_4Week_WM",
                "id": "6eb0fc41-e82e-4e7d-9736-c87e2c4fe258"
            },
            {
                "collectiveLaborAgreement": {
                    "comment": "<br>",
                    "description": "QA_PayrollConfiguration_Week_CLA",
                    "id": "c0a9de91-390a-4f12-967a-a17dfbc94cc7"
                },
                "comment": "<br>",
                "description": "QA_PayrollConfiguration_Week_WM",
                "id": "bf093626-7201-4e56-89d4-2d8760728771"
            },
            {
                "collectiveLaborAgreement": {
                    "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!",
                    "description": "QA_PayrollConfiguration1_CLA_ForLevel_PA",
                    "id": "dfdaa0a5-bce8-4ddd-8e1c-42554334c342"
                },
                "comment": "Empty WM for unit and integration tests. DO NOT EDIT!",
                "description": "QA_PayrollConfiguration1_WM_ForLevel_PA",
                "id": "5e010bed-611d-469b-a220-5253e3896f47"
            },
            {
                "collectiveLaborAgreement": {
                    "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!",
                    "description": "QA_PayrollConfiguration1_CLA_ForLevel_WM",
                    "id": "8c87ffab-20e3-4bcd-808c-8eb9f4e8dfdf"
                },
                "comment": "Empty WM for unit and integration tests. DO NOT EDIT!",
                "description": "QA_PayrollConfiguration1_WM_ForLevel_WM",
                "id": "a77d7e45-2612-450a-86d0-485baecee6ad"
            }
        ]
    },
    QA_AKPayrollConfiguration2: {
        _embedded: [
            {
                "id": "b2dd38be-45fa-4a64-b21e-df1911cc3e07",
                "description": "QA_PayrollConfiguration2_WM_ForLevel_PA",
                "comment": "Empty WM for unit and integration tests. DO NOT EDIT!",
                "collectiveLaborAgreement": {
                    "id": "9ad57900-980e-404a-b8a3-14eca3278552",
                    "description": "QA_PayrollConfiguration2_CLA_ForLevel_PA",
                    "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
                }
            },
            {
                "id": "382288d6-fc7b-4510-ba0b-e17f2313d721",
                "description": "QA_PayrollConfiguration2_WM_ForLevel_WM",
                "comment": "Empty WM for unit and integration tests. DO NOT EDIT!",
                "collectiveLaborAgreement": {
                    "id": "969d1c9a-ca97-40d1-851d-048177826eac",
                    "description": "QA_PayrollConfiguration2_CLA_ForLevel_WM",
                    "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
                }
            }
        ]
    },
};
