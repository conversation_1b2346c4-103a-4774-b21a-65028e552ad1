import {testEndpoint, ITestConfig, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./PayrollComponent.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrollcomponents/{payrollComponentId}",
    method: "DELETE",
    params: {
        payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponent_DELETE_CLA.year2025.component_60,
    },
    modules: {
        methodNotAllowed: ["GET", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "DeletePayrollComponentByPayrollComponentId should return 400 and the correct message for component used in unit percentage": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponent_DELETE_CLA.year2025.component_60,
                },
                timeout: 120000,
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 2,
                        mode: ValidationMode.count
                    },
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_Delete_EntityHasChildren",
                        mode: ValidationMode.strict
                    },
                    {
                        key: "messages[1].messageCode",
                        value: "API_PayrollConfiguration_PayrollComponent_Delete_52007",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "DeletePayrollComponentByPayrollComponentId should return 403 when payrollComponentId=non-payrollComponentGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
