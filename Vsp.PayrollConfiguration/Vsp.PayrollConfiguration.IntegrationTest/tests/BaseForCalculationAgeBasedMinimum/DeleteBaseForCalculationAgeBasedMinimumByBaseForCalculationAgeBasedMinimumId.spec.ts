import {testEndpoint, ITestConfig, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculationAgeBasedMinimum.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "basesforcalculation/agebasedminimums/{baseForCalculationAgeBasedMinimumId}",
    method: "DELETE",
    params: {
        baseForCalculationAgeBasedMinimumId: entityData.baseForCalculationAgeBasedMinimumIds.QA_BFC_AgeBasedMinimum_DELETE_CLA.year2025.ageBasedMinimum_35,
    },
    modules: {
        methodNotAllowed: ["GET", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        "DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumId should return 400 and EntityHasChildren message": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationAgeBasedMinimumId: entityData.baseForCalculationAgeBasedMinimumIds.QA_BFC_AgeBasedMinimum_DELETE_CLA.year2025.ageBasedMinimum_35,
                },
                timeout: 120000,
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 1,
                        mode: ValidationMode.count
                    },
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_Delete_EntityHasChildren",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "DeleteBaseForCalculationAgeBasedMinimumByBaseForCalculationAgeBasedMinimumId should return 403 when baseForCalculationAgeBasedMinimumId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationAgeBasedMinimumId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
