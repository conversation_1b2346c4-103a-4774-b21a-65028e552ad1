export const collectiveLaborAgreementIds = {
    QA_UnitPercentage_GET_CLA: "97c1203c-86bc-4f00-8d89-e5b29df68220",
    QA_UnitPercentage_DELETE_CLA_ForLevel_CLA: "9607aaf3-02b0-441e-a1ab-91f10cbf7a9d",
    QA_UnitPercentage_PATCH_CLA_ForLevel_CLA: "d9e9b2ab-4ff6-4193-b3c0-a41f5815971c",
    QA_UnitPercentage_POST_CLA_ForLevel_CLA: "53843707-7a6c-4f82-b445-f36636fb581a",
};

export const wageModelIds = {
    QA_UnitPercentage_GET_WM: "225cd7f8-5c93-4947-b352-cdd8231163b3",
    QA_UnitPercentage_DELETE_WM_ForLevel_WM: "cb3efffd-d2a4-4d28-a6c3-32d2d241b413",
    QA_UnitPercentage_PATCH_WM_ForLevel_WM: "8aa875b4-e58c-4837-83a2-55ef01a384f2",
    QA_UnitPercentage_POST_WM_ForLevel_WM: "973a7a96-0449-4bf3-95f4-3ee05bb8cc29",
};

export const payrollAdministrationIds = {
    QA_UnitPercentage_GET_PA: "f056bc70-858d-4f7f-8197-827484abc924",
    QA_UnitPercentage_POST_PA_ForLevel_PA: "d7764980-7211-4e63-b871-a516913ba872",
    QA_UnitPercentage_DELETE_PA_ForLevel_PA: "543a86b2-3ee2-4c23-83fe-1bb90747d227",
    QA_UnitPercentage_PATCH_PA_ForLevel_PA: "d134e58d-0d60-4f6d-8762-01ba0a1f84bd",
};

export const yearIds = {
    QA_UnitPercentage_GET_CLA: {
        year2025: "000008fb-07e9-0000-0000-000000000000"
    },
    QA_UnitPercentage_GET_WM: {
        year2025: "000008fc-07e9-0000-0000-000000000000"
    },
    QA_UnitPercentage_GET_PA: {
        year2025: "000008fd-07e9-0000-0000-000000000000"
    },
};

export const unitPercentageIds = {
    QA_UnitPercentage_PATCH_PA_ForLevel_PA: {
        Year2025Component42Period1: "00000912-07e9-002a-0100-000000000000",
    },
    QA_UnitPercentage_DELETE_PA_ForLevel_PA: {
        Year2025Component42Period1: "00000906-07e9-002a-0100-000000000000",
    },
};
