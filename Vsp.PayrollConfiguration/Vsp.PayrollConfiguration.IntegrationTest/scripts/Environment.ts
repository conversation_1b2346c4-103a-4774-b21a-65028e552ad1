enum Environment {
    Local = 1,
    Ontw = 2,
    DevOps = 3,
};

// Pas deze setting lokaal aan om de tests te draaien tegen een specifieke instantie van de service.
// NB: Dit moet altijd gecommit blijven op een werkende deployment waarde: niet Local!
let environment = Cypress.env('ENVIRONMENT') ?? Environment.Ontw;

export function getServiceUrl(): string {
    switch (environment) {
        case Environment.Local: return "https://localhost:45007/";
        case Environment.Ontw: return "https://api.loket-ontw.nl/payrollconfiguration/";
        case Environment.DevOps: return "#{LoketApiUrl}#payrollconfiguration/";
    }
};

export function getAuthUrl(): string {
    switch (environment) {
        case Environment.Local:
        case Environment.Ontw: return "https://oauth.loket-ontw.nl/";
        case Environment.DevOps: return "#{AuthenticationServiceExternalUrl}#";
    }
};

export function getPersonUrl(): string {
    switch (environment) {
        case Environment.Local:
        case Environment.Ontw: return "https://api.loket-ontw.nl/person/";
        case Environment.DevOps: return "#{LoketApiUrl}#person/";
    }
};

export function getGlobalFilterUrl(): string {
    switch (environment) {
        case Environment.Local:
        case Environment.Ontw: return "https://api.loket-ontw.nl/globalfilter/";
        case Environment.DevOps: return "#{LoketApiUrl}#globalfilter/";
    }
};
