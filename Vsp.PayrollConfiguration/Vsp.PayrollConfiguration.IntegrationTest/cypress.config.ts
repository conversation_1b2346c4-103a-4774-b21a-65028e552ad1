import { defineConfig } from 'cypress'

export default defineConfig({
    fixturesFolder: 'fixtures',
    numTestsKeptInMemory: 100,
    reporter: 'junit',
    reporterOptions: {
        mochaFile: 'test-output/test-output-[hash].xml',
        toConsole: true,
        attachments: true,
        jenkinsMode: true,
    },
    requestTimeout: 120000,
    responseTimeout: 120000,
    screenshotOnRunFailure: false,
    video: false,
    watchForFileChanges: false,
    e2e: {
        experimentalRunAllSpecs: true,
        specPattern: 'tests/**/*.spec.ts',
        supportFile: false,
        viewportHeight: 0,
        viewportWidth: 0,
        watchForFileChanges: false,
    },
})
