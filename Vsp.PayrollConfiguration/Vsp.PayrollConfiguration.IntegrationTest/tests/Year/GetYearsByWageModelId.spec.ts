import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./Year.data";
import * as endpointData from "./GetYearsByWageModelId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "wagemodels/{wageModelId}/years",
    method: "GET",
    params: {
        wageModelId: entityData.wageModelIds.QA_Year_GET_WM,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        "GetYearsByWageModelId should return 200, the correct resourceVersion+obsoleteDate and the correct list of Years for QA_Year_GET_WM to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    wageModelId: entityData.wageModelIds.QA_Year_GET_WM,
                },
                queryStringParams: {
                    orderBy: "year"
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_Year_GET_WM._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_WM._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_WM._embedded,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        "GetYearsByWageModelId x-reportinput should return 200 and the correct report of Years": {
            request: {
                headers: {
                    "accept": "text/csv",
                    'x-reportinput': '{"FileNameWithoutExtension":"YearsByWageModel","Fields":[{"FieldName":"year","ReportColumnName":"Jaar"},{"FieldName":"payrollPeriodType.value","ReportColumnName":"Verloningstype"},{"FieldName":"definedAtLevel.payrollPeriodType.value","ReportColumnName":"Verloningstype niveau"},{"FieldName":"inheritanceLevel.type.value","ReportColumnName":"Niveau"},{"FieldName":"standardShift.shiftNumber","ReportColumnName":"Ploeg Nr."},{"FieldName":"standardShift.fullTimeHoursPerWeek","ReportColumnName":"Ploeg uren"},{"FieldName":"standardShift.bonusPercentage","ReportColumnName":"Ploeg bonuspercentage"},{"FieldName":"definedAtLevel.standardShift.value","ReportColumnName":"Ploeg niveau"},{"FieldName":"standardEmployeeProfile.employeeProfileNumber","ReportColumnName":"Werknemerprofiel Nr."},{"FieldName":"definedAtLevel.standardEmployeeProfile.value","ReportColumnName":"Werknemerprofiel niveau"},{"FieldName":"id","ReportColumnName":"LoketID"}]}',
                    "content-type": "application/json",
                },
                token: "QA_PayrollConfiguration1",
                params: {
                    wageModelId: entityData.wageModelIds.QA_Year_GET_WM,
                },
                queryStringParams: {
                    orderBy: "year"
                },
            },
            response: {
                code: 200,
                headers: {
                    "content-disposition": new RegExp("attachment; filename=\"YearsByWageModel " + sharedData.sharedCurrentDate + " ([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9].csv\""),
                    "content-type": "text/csv; charset=utf-8",
                },
                validations: [
                    {
                        value: "Jaar;Verloningstype;Verloningstype niveau;Niveau;Ploeg Nr.;Ploeg uren;Ploeg bonuspercentage;Ploeg niveau;Werknemerprofiel Nr.;Werknemerprofiel niveau;LoketID\r\n2020;4 Weken;WageModel;WageModel;15;15,25;15,025;WageModel;1;WageModel;0000093c-07e4-0000-0000-000000000000\r\n2021;4 Weken;WageModel;WageModel;1;40,21;0,021;CollectiveLaborAgreement;;CollectiveLaborAgreement;0000093c-07e5-0000-0000-000000000000\r\n2022;4 Weken;WageModel;WageModel;;;;WageModel;;CollectiveLaborAgreement;0000093c-07e6-0000-0000-000000000000\r\n2023;4 Weken;WageModel;WageModel;2;38,23;5,023;WageModel;1;WageModel;0000093c-07e7-0000-0000-000000000000\r\n2024;4 Weken;WageModel;WageModel;2;38,24;5,024;WageModel;1;WageModel;0000093c-07e8-0000-0000-000000000000\r\n2025;4 Weken;WageModel;WageModel;11;38,25;5,025;WageModel;1;WageModel;0000093c-07e9-0000-0000-000000000000\r\n",
                        mode: ValidationMode.strict,
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetYearsByWageModelId should return 403 when wageModelId=non-wageModelGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    wageModelId: sharedData.nonEntityGuid
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonInheritanceGuid,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
