import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./PayrollComponent.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrollcomponents",
    method: "POST",
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT"],
        authorization: true,
        rechtBasis: false,
        queryStringParams: {
            collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_PayrollComponent_POST_CLA,
        },
        token: "QA_PayrollConfiguration1",
        requireBody: false,     // Covered in C# integration tests
        body: {
            "year": 2025,
            "key": 1
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---201---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger validation
        "PostPayrollComponentsByInheritanceLevelId should return 400 and the correct message when key=invalid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                queryStringParams: {
                    collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_PayrollComponent_POST_CLA,                },
                body: {
                    "year": 2025,
                    "key": 9999999
                },
                timeout: 120000
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages[0]",
                        value: {
                            "messageCode": "API_PayrollConfiguration_PayrollComponent_Post_Invalid"
                        },
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PostPayrollComponentsByInheritanceLevelId should return 403 when yearId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                queryStringParams: {
                    collectiveLaborAgreementId: sharedData.nonEntityGuid,
                },
                body: {
                    "year": 2025,
                    "key": 1
                }
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonInheritanceGuid,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
