PRINT '  START PostCheck Inheritance Entities'

BEGIN TRANSACTION

-- DEFI<PERSON>
declare @werkgeverIDs table ( WerkgeverID int )
insert into @werkgeverIDs values
( 2238 ), -- QA_PayrollConfiguration_CLA_ForLevel_CLA

( 2239 ), -- QA_PayrollConfiguration1_CLA_ForLevel_WM
( 2243 ), -- QA_PayrollConfiguration1_WM_ForLevel_WM

( 2240 ), -- QA_PayrollConfiguration2_CLA_ForLevel_WM
( 2244 ), -- QA_PayrollConfiguration2_WM_ForLevel_WM

( 2241 ), -- QA_PayrollConfiguration1_CLA_ForLevel_PA
( 2245 ), -- QA_PayrollConfiguration1_WM_ForLevel_PA
( 2247 ), -- QA_PayrollConfiguration1_PA_ForLevel_PA

( 2242 ), -- QA_PayrollConfiguration2_CLA_ForLevel_PA
( 2246 ), -- QA_PayrollConfiguration2_WM_ForLevel_PA
( 2248 )  -- QA_PayrollConfiguration2_PA_ForLevel_PA

-- VIEW
select mw.WerkgeverID, lower(mw.ExternID) as ExternID, isnull(mw.Omschrijving, w.Naam) as OmschrijvingOfNaam, n.Type
from Ulsa.ModelWerkgever mw
	join Ulsa.WerkgeverNiveau n on n.WerkgeverID = mw.WerkgeverID
	left join Ulsa.Werkgever w on w.WerkgeverID = mw.WerkgeverID
where mw.WerkgeverID in (select WerkgeverID from @werkgeverIDs)
order by n.Type, OmschrijvingOfNaam

-- DELETE
delete from Ulsa.ModelPloeg where WerkgeverID in (select WerkgeverID from @werkgeverIDs)
delete from Ulsa.ModelComponent where WerkgeverID in (select WerkgeverID from @werkgeverIDs)
delete from Ulsa.ModelEenheidPercentage where WerkgeverID in (select WerkgeverID from @werkgeverIDs)
-- TODO: Other inheritance entities!

COMMIT TRANSACTION
GO;