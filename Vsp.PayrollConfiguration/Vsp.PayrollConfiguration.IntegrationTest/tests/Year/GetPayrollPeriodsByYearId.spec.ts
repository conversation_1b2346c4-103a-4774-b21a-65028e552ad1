import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./Year.data";
import * as endpointData from "./GetPayrollPeriodsByYearId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "years/{yearId}/payrollperiods",
    method: "GET",
    params: {
        yearId: entityData.yearIds.QA_Year_GET_CLA.year2025,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        "GetPayrollPeriodsByYearId (CLA) should return 200, the correct resourceVersion+obsoleteData and the correct list of Payroll Periods(12) in 2025 for collective labor agreement: QA_PayrollConfiguration1_CLA_ForLevel_CLA(type: month) to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.CollectiveLaborAgreement.QA_PayrollConfiguration1_CLA_ForLevel_CLA.year2025,
                },
                queryStringParams: {
                    orderBy: "periodNumber",
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_CLA_ForLevel_CLA.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_CLA_ForLevel_CLA.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_CLA_ForLevel_CLA.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetPayrollPeriodsByYearId (WM) should return 200 and the correct list of Payroll Periods(12) in 2025 for wage model: QA_PayrollConfiguration1_WM_ForLevel_WM(type: month) to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.WageModel.QA_PayrollConfiguration1_WM_ForLevel_WM.year2025,
                },
                queryStringParams: {
                    orderBy: "periodNumber",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_WM_ForLevel_WM.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_WM_ForLevel_WM.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_WM_ForLevel_WM.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetPayrollPeriodsByYearId (PA) should return 200 and the correct list of Payroll Periods(12) in 2025 for payroll administration: QA_PayrollConfiguration1_PA_ForLevel_PA(type: month) to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.PayrollAdministration.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025,
                },
                queryStringParams: {
                    orderBy: "periodNumber",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetPayrollPeriodsByYearId (CLA) should return 200 and the correct list of Payroll Periods(13) in 2020 for collective labor agreement: QA_PayrollConfiguration1_CLA_ForLevel_CLA_4Week(type: four weeks)": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.CollectiveLaborAgreement.QA_PayrollConfiguration1_CLA_ForLevel_CLA_4Week.year2025,
                },
                queryStringParams: {
                    orderBy: "periodNumber",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_CLA_ForLevel_CLA_4Week.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_CLA_ForLevel_CLA_4Week.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_CLA_ForLevel_CLA_4Week.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetPayrollPeriodsByYearId (CLA) should return 200 and the correct list of Payroll Periods(53) in 2020 for payroll administration: QA_PayrollConfiguration1_CLA_ForLevel_CLA_Week(type week)": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.CollectiveLaborAgreement.QA_PayrollConfiguration1_CLA_ForLevel_CLA_Week.year2025,
                },
                queryStringParams: {
                    orderBy: "periodNumber",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_CLA_ForLevel_CLA_Week.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_CLA_ForLevel_CLA_Week.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_CLA_ForLevel_CLA_Week.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetPayrollPeriodsByYearId with filter='periodNumber gt 1 AND periodNumber lt 4' should return 200 and the filtered list of Payroll Periods": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.PayrollAdministration.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025,
                },
                queryStringParams: {
                    filter: "periodNumber gt 1 AND periodNumber lt 4",
                    orderBy: "periodNumber",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "totalSize",
                        value: 2,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: 2,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded[0].payrollPeriodId",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025._embedded[1].payrollPeriodId,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded[1].payrollPeriodId",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025._embedded[2].payrollPeriodId,
                        mode: ValidationMode.strict,
                    },
                ]
            }
        },
        "GetPayrollPeriodsByYearId with orderBy='-periodNumber' should return 200 and the ordered list of Payroll Periods": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.PayrollAdministration.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025,
                },
                queryStringParams: {
                    filter: "periodNumber gt 1 AND periodNumber lt 4",
                    orderBy: "-periodNumber",
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "_embedded[0].payrollPeriodId",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025._embedded[2].payrollPeriodId,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded[1].payrollPeriodId",
                        value: endpointData.expectedResponses.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025._embedded[1].payrollPeriodId,
                        mode: ValidationMode.strict,
                    },
                ]
            }
        },
        "GetPayrollPeriodsByYearId with x-reportinput should return 200 and the correct report of Payroll Periods": {
            request: {
                headers: {
                    "accept": "text/csv",
                    "x-reportinput": '{"FileNameWithoutExtension":"PayrollPeriodsByYear","Fields":[{"FieldName":"year","ReportColumnName":"Jaar"},{"FieldName":"periodNumber","ReportColumnName":"Periode nummer"},{"FieldName":"payrollPeriodId","ReportColumnName":"Period Id"},{"FieldName":"periodStartDate","ReportColumnName":"Start datum"},{"FieldName":"periodEndDate","ReportColumnName":"Eind datum"}]}',
                    "content-type": "application/json",
                },
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.PayrollAdministration.QA_PayrollConfiguration1_PA_ForLevel_PA.year2025,
                },
                queryStringParams: {
                    filter: "periodNumber gt 1 AND periodNumber lt 4",
                    orderBy: "-periodNumber",
                },
            },
            response: {
                code: 200,
                headers: {
                    "content-disposition": new RegExp("attachment; filename=\"PayrollPeriodsByYear " + sharedData.sharedCurrentDate + " ([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9].csv\""),
                    "content-type": "text/csv; charset=utf-8",
                },
                validations: [
                    {
                        value: "Jaar;Periode nummer;Period Id;Start datum;Eind datum\r\n2025;3;202503;2025-03-01;2025-03-31\r\n2025;2;202502;2025-02-01;2025-02-28\r\n",
                        mode: ValidationMode.strict,
                    },
                ],
            }
        },
        "GetPayrollPeriodsByYearId (CLA) should return 200 and the correct list of Payroll Periods for 2025 at collective labor agreement level to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_Year_GET_CLA.year2025,
                },
                queryStringParams: {
                    orderBy: "payrollPeriodId"
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        "GetPayrollPeriodsByYearId (WM) should return 200 and the correct list of Payroll Periods for 2025 at wage model level": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_Year_GET_WM.year2025,
                },
                queryStringParams: {
                    orderBy: "payrollPeriodId"
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_Year_GET_WM.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_WM.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_WM.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetPayrollPeriodsByYearId (PA) should return 200 and the correct list of Payroll Periods for 2025 at payroll administration level": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_Year_GET_PA.year2025,
                },
                queryStringParams: {
                    orderBy: "payrollPeriodId"
                },
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_Year_GET_PA.year2025._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_PA.year2025._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_PA.year2025._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
        "GetPayrollPeriodsByYearId with x-reportinput should return 200 and the correct report of PayrollPeriods": {
            request: {
                headers: {
                    "accept": "text/csv",
                    'x-reportinput': '{"FileNameWithoutExtension":"PayrollPeriodsByYear","Fields":[{"FieldName":"year","ReportColumnName":"Jaar"},{"FieldName":"periodNumber","ReportColumnName":"Periode"},{"FieldName":"payrollPeriodId","ReportColumnName":"Periode ID"},{"FieldName":"periodStartDate","ReportColumnName":"Begindatum"},{"FieldName":"periodEndDate","ReportColumnName":"Einddatum"}]}',
                    "content-type": "application/json",
                },
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: entityData.yearIds.QA_Year_GET_CLA.year2025,
                },
                queryStringParams: {
                    orderBy: "payrollPeriodId"
                },
            },
            response: {
                code: 200,
                headers: {
                    "content-disposition": new RegExp("attachment; filename=\"PayrollPeriodsByYear " + sharedData.sharedCurrentDate + " ([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9].csv\""),
                    "content-type": "text/csv; charset=utf-8",
                },
                validations: [
                    {
                        value: "Jaar;Periode;Periode ID;Begindatum;Einddatum\r\n2025;1;202501;2025-01-01;2025-01-31\r\n2025;2;202502;2025-02-01;2025-02-28\r\n2025;3;202503;2025-03-01;2025-03-31\r\n2025;4;202504;2025-04-01;2025-04-30\r\n2025;5;202505;2025-05-01;2025-05-31\r\n2025;6;202506;2025-06-01;2025-06-30\r\n2025;7;202507;2025-07-01;2025-07-31\r\n2025;8;202508;2025-08-01;2025-08-31\r\n2025;9;202509;2025-09-01;2025-09-30\r\n2025;10;202510;2025-10-01;2025-10-31\r\n2025;11;202511;2025-11-01;2025-11-30\r\n2025;12;202512;2025-12-01;2025-12-31\r\n",
                        mode: ValidationMode.strict,
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetPayrollPeriodsByYearId should return 403 when yearId=non-yearGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    yearId: sharedData.nonEntityGuid
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
