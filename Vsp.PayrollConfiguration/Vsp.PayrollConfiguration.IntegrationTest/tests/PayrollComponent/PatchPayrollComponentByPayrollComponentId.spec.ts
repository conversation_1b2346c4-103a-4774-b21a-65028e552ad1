import {ITestConfig, testEndpoint, ValidationMode} from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./PayrollComponent.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "payrollcomponents/{payrollComponentId}",
    method: "PATCH",
    params: {
        payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponent_PATCH_CLA.year2025.component_673,
    },
    modules: {
        methodNotAllowed: ["GET", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
        requireBody: false,     // Covered in C# integration tests
        token: "QA_PayrollConfiguration1",
        body: {
            "description": "FISC.BYT.AUTO",
            "deductionOrPayment": null,
            "paymentPeriod": { "key": 7 },
            "taxLiable": { "key": 1 },
            "socialSecurityLiable": { "key": 1 },
            "hoursIndication": null,
            "costsEmployer": null,
            "isNetToGross": false,
            "isFullTime": false,
            "isBaseForCalculationOvertime": false,
            "isTravelExpense": false,
            "suppressPrinting": false,
            "suppressPrintingAccumulations": false,
            "isBaseForCalculationDailyWageZw": false,
            "isBaseForCalculationDailyWageSupplement": false,
            "baseForCalculationBter": null,
            "isPayment": true, // invalid value, should be false for this component
            "paymentDescription": null,
            "isOvertime": false
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "PatchPayrollComponentByPayrollComponentId should return 400 and the correct message for invalid IsPayment value": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: entityData.payrollComponentIds.QA_PayrollComponent_PATCH_CLA.year2025.component_673,
                },
                timeout: 120000,
                body: {
                    "description": "FISC.BYT.AUTO",
                    "deductionOrPayment": null,
                    "paymentPeriod": { "key": 7 },
                    "taxLiable": { "key": 1 },
                    "socialSecurityLiable": { "key": 1 },
                    "hoursIndication": null,
                    "costsEmployer": null,
                    "isNetToGross": false,
                    "isFullTime": false,
                    "isBaseForCalculationOvertime": false,
                    "isTravelExpense": false,
                    "suppressPrinting": false,
                    "suppressPrintingAccumulations": false,
                    "isBaseForCalculationDailyWageZw": false,
                    "isBaseForCalculationDailyWageSupplement": false,
                    "baseForCalculationBter": null,
                    "isPayment": true, // invalid value, should be false for this component
                    "paymentDescription": null,
                    "isOvertime": false
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_PayrollComponent_IsPayment_1",
                        mode: ValidationMode.strict
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PatchPayrollComponentByPayrollComponentId should return 403 when payrollComponentId=non-componentId": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    payrollComponentId: sharedData.nonEntityGuid,
                },
                body: {
                    "description": "FISC.BYT.AUTO",
                    "deductionOrPayment": null,
                    "paymentPeriod": { "key": 7 },
                    "taxLiable": { "key": 1 },
                    "socialSecurityLiable": { "key": 1 },
                    "hoursIndication": null,
                    "costsEmployer": null,
                    "isNetToGross": false,
                    "isFullTime": false,
                    "isBaseForCalculationOvertime": false,
                    "isTravelExpense": false,
                    "suppressPrinting": false,
                    "suppressPrintingAccumulations": false,
                    "isBaseForCalculationDailyWageZw": false,
                    "isBaseForCalculationDailyWageSupplement": false,
                    "baseForCalculationBter": null,
                    "isPayment": false,
                    "paymentDescription": null,
                    "isOvertime": false
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});