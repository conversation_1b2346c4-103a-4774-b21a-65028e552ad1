import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as endpointData from "./GetWageModelsByBearerToken.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "wagemodels",
    method: "GET",
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_AKPayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        "GetWageModelsByBearerToken should return 200, the correct resourceVersion+obsoleteDate and the correcte list of Wage Models to provider user QA_AKPayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_AKPayrollConfiguration1",
                queryStringParams: {
                    filter: "description lk 'QA_PayrollConfiguration'",
                    orderBy: "description",
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_AKPayrollConfiguration1._embedded.length,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_AKPayrollConfiguration1._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_AKPayrollConfiguration1._embedded,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        }
        // request with x-reportinput not necessary, covered in C# integration tests
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
