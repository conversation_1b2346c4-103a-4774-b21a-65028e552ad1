import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./UnitPercentage.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "unitpercentages/{unitPercentageId}",
    method: "DELETE",
    params: {
        unitPercentageId: entityData.unitPercentageIds.QA_UnitPercentage_DELETE_PA_ForLevel_PA.Year2025Component42Period1
    },
    modules: {
        methodNotAllowed: ["GET", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger a validation
        "DeleteUnitPercentageByUnitPercentageId should return 400 for unitPercentage of period 1 having a unitpercentege modification defined for period > 1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    unitPercentageId: entityData.unitPercentageIds.QA_UnitPercentage_DELETE_PA_ForLevel_PA.Year2025Component42Period1
                },
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages",
                        value: 1,
                        mode: ValidationMode.count
                    },
                    {
                        key: "messages[0].messageCode",
                        value: "API_PayrollConfiguration_Delete_PayrollPeriod_FirstPeriodCannotBeDeleted",
                        mode: ValidationMode.strict
                    }
                ],
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "DeleteUnitPercentageByUnitPercentageId should return 403 when unitPercentageId=non-unitPercentageGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    unitPercentageId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
