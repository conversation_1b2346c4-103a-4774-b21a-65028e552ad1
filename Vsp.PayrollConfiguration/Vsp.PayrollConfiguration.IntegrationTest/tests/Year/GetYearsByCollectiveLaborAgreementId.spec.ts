import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./Year.data";
import * as endpointData from "./GetYearsByCollectiveLaborAgreementId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "collectivelaboragreements/{collectiveLaborAgreementId}/years",
    method: "GET",
    params: {
        collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_Year_GET_CLA,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT", "POST"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1,
    },
    tests: {
        // ---200---
        "GetYearsByCollectiveLaborAgreementId should return 200, the correct resourceVersion+obsoleteDate and the correct list of Years for QA_Year_GET_CLA to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    collectiveLaborAgreementId: entityData.collectiveLaborAgreementIds.QA_Year_GET_CLA,
                },
                queryStringParams: {
                    orderBy: "year"
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_Year_GET_CLA._embedded,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        "GetYearsByCollectiveLaborAgreementId x-reportinput should return 200 and the correct report of Years": {
            request: {
                headers: {
                    "accept": "text/csv",
                    'x-reportinput': '{"FileNameWithoutExtension":"YearsByCollectiveLaborAgreement","Fields":[{"FieldName":"year","ReportColumnName":"Jaar"},{"FieldName":"payrollPeriodType.value","ReportColumnName":"Verloningstype"},{"FieldName":"definedAtLevel.payrollPeriodType.value","ReportColumnName":"Verloningstype niveau"},{"FieldName":"inheritanceLevel.type.value","ReportColumnName":"Niveau"},{"FieldName":"standardShift.shiftNumber","ReportColumnName":"Ploeg Nr."},{"FieldName":"standardShift.fullTimeHoursPerWeek","ReportColumnName":"Ploeg uren"},{"FieldName":"standardShift.bonusPercentage","ReportColumnName":"Ploeg bonuspercentage"},{"FieldName":"definedAtLevel.standardShift.value","ReportColumnName":"Ploeg niveau"},{"FieldName":"standardEmployeeProfile.employeeProfileNumber","ReportColumnName":"Werknemerprofiel Nr."},{"FieldName":"definedAtLevel.standardEmployeeProfile.value","ReportColumnName":"Werknemerprofiel niveau"},{"FieldName":"id","ReportColumnName":"LoketID"}]}',
                    "content-type": "application/json",
                },
                token: "QA_PayrollConfiguration1",
                queryStringParams: {
                    payrollAdministrationId: entityData.payrollAdministrationIds.QA_Year_GET_PA,
                    orderBy: "year"
                },
            },
            response: {
                code: 200,
                headers: {
                    "content-disposition": new RegExp("attachment; filename=\"YearsByCollectiveLaborAgreement " + sharedData.sharedCurrentDate + " ([0-1]?[0-9]|2[0-3]):[0-5][0-9]:[0-5][0-9].csv\""),
                    "content-type": "text/csv; charset=utf-8",
                },
                validations: [
                    {
                        value: "Jaar;Verloningstype;Verloningstype niveau;Niveau;Ploeg Nr.;Ploeg uren;Ploeg bonuspercentage;Ploeg niveau;Werknemerprofiel Nr.;Werknemerprofiel niveau;LoketID\r\n2020;Maand;CollectiveLaborAgreement;CollectiveLaborAgreement;1;40,20;0,020;CollectiveLaborAgreement;;CollectiveLaborAgreement;0000093b-07e4-0000-0000-000000000000\r\n2021;Maand;CollectiveLaborAgreement;CollectiveLaborAgreement;1;40,21;0,021;CollectiveLaborAgreement;;CollectiveLaborAgreement;0000093b-07e5-0000-0000-000000000000\r\n2022;Maand;CollectiveLaborAgreement;CollectiveLaborAgreement;1;40,22;0,022;CollectiveLaborAgreement;;CollectiveLaborAgreement;0000093b-07e6-0000-0000-000000000000\r\n2023;Maand;CollectiveLaborAgreement;CollectiveLaborAgreement;1;40,23;0,023;CollectiveLaborAgreement;;CollectiveLaborAgreement;0000093b-07e7-0000-0000-000000000000\r\n2024;Maand;CollectiveLaborAgreement;CollectiveLaborAgreement;1;40,24;0,024;CollectiveLaborAgreement;;CollectiveLaborAgreement;0000093b-07e8-0000-0000-000000000000\r\n2025;Maand;CollectiveLaborAgreement;CollectiveLaborAgreement;1;40,25;0,025;CollectiveLaborAgreement;;CollectiveLaborAgreement;0000093b-07e9-0000-0000-000000000000\r\n",
                        mode: ValidationMode.strict,
                    },
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "GetYearsByCollectiveLaborAgreementId should return 403 when collectiveLaborAgreementId=non-collectiveLaborAgreementGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    collectiveLaborAgreementId: sharedData.nonEntityGuid
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonInheritanceGuid,
                        mode: ValidationMode.partial,
                    },
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});
