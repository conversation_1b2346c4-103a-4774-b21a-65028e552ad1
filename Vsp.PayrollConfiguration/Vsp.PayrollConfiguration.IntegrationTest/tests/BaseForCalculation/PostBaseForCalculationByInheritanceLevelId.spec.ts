import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculation.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "basesforcalculation",
    method: "POST",
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "PUT"],
        authorization: true,
        rechtBasis: false,
        queryStringParams: {
            payrollAdministrationId: entityData.payrollAdministrationIds.QA_BaseForCalculation_POST_PA,
        },
        token: "QA_PayrollConfiguration1",
        requireBody: false,     // Covered in C# integration tests
        body: {
            "key": 16,
            "year": 2025,
            "description": "EMPTY",
            "startPayrollPeriod": {
                "periodNumber": 1
            },
            "baseType": null,
            "startEmployeeAgeType": null,
            "startEmployeeAge": 0.0,
            "endEmployeeAgeType": null,
            "endEmployeeAge": 0.0,
            "resultPayrollComponent": {
                "key": 257
            },
            "percentage": 0.0,
            "calculationPayrollPeriod": null,
            "referencePayrollPeriod": null,
            "payoutPayrollPeriod": null,
            "accrualEndPayrollPeriod": null,
            "payslipType": {
                "key": 4
            },
            "isPayoutAtStartOfEmployment": false,
            "isPayoutAtEndOfEmployment": false,
            "advancePayrollComponent": null,
            "advancePercentage": 0.0,
            "advancePayrollPeriod": null,
            "periodicReservationPayrollComponent": null,
            "financialReservationPercentage": 0.0,
            "financialMarkupPercentage": 0.0,
            "isCumulativeCalculation": true,
            "isPartTimeCalculation": false,
            "isAutomaticCalculation": false,
            "isSupplementingDailyWage": false,
            "minimumMaximumType": {
                "key": 2
            }
        },
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---201---
        // Destructive operations are covered in C# integration tests (within a transaction) - to avoid corrupting test data!
        // ---400---
        // just one check to trigger validation
        "PostBaseForCalculationByInheritanceLevelId should return 400 and the correct message when key=invalid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                queryStringParams: {
                    payrollAdministrationId: entityData.payrollAdministrationIds.QA_BaseForCalculation_POST_PA,
                },
                body: {
                    "key": 16,
                    "year": 2025,
                    "description": "EMPTY",
                    "startPayrollPeriod": {
                        "periodNumber": 1
                    },
                    "baseType": null,
                    "startEmployeeAgeType": null,
                    "startEmployeeAge": 0.0,
                    "endEmployeeAgeType": null,
                    "endEmployeeAge": 0.0,
                    "resultPayrollComponent": {
                        "key": 257
                    },
                    "percentage": 0.0,
                    "calculationPayrollPeriod": null,
                    "referencePayrollPeriod": null,
                    "payoutPayrollPeriod": null,
                    "accrualEndPayrollPeriod": null,
                    "payslipType": {
                        "key": 4
                    },
                    "isPayoutAtStartOfEmployment": false,
                    "isPayoutAtEndOfEmployment": false,
                    "advancePayrollComponent": null,
                    "advancePercentage": 0.0,
                    "advancePayrollPeriod": null,
                    "periodicReservationPayrollComponent": null,
                    "financialReservationPercentage": 0.0,
                    "financialMarkupPercentage": 0.0,
                    "isCumulativeCalculation": true,
                    "isPartTimeCalculation": false,
                    "isAutomaticCalculation": false,
                    "isSupplementingDailyWage": false,
                    "minimumMaximumType": {
                        "key": 2
                    }
                },
                timeout: 120000
            },
            response: {
                code: 400,
                validations: [
                    {
                        key: "messages[0]",
                        value: {
                            "messageCode": "API_PayrollConfiguration_BaseForCalculation_Post_Key_ReservedCLA"
                        },
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
        // ---403---
        // request as employer user not necessary, authorization is checked elsewhere
        // request as department manager not necessary, authorization is checked elsewhere
        // request as employee user not necessary, authorization is checked elsewhere
        // just one check to trigger authorization
        "PostBaseForCalculationByInheritanceLevelId should return 403 when payrollAdministrationId=non-payrollAdministrationGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                queryStringParams: {
                    payrollAdministrationId: sharedData.nonEntityGuid,
                },
                body: {
                    "key": 16,
                    "year": 2025,
                    "description": "EMPTY",
                    "startPayrollPeriod": {
                        "periodNumber": 1
                    },
                    "baseType": null,
                    "startEmployeeAgeType": null,
                    "startEmployeeAge": 0.0,
                    "endEmployeeAgeType": null,
                    "endEmployeeAge": 0.0,
                    "resultPayrollComponent": {
                        "key": 257
                    },
                    "percentage": 0.0,
                    "calculationPayrollPeriod": null,
                    "referencePayrollPeriod": null,
                    "payoutPayrollPeriod": null,
                    "accrualEndPayrollPeriod": null,
                    "payslipType": {
                        "key": 4
                    },
                    "isPayoutAtStartOfEmployment": false,
                    "isPayoutAtEndOfEmployment": false,
                    "advancePayrollComponent": null,
                    "advancePercentage": 0.0,
                    "advancePayrollPeriod": null,
                    "periodicReservationPayrollComponent": null,
                    "financialReservationPercentage": 0.0,
                    "financialMarkupPercentage": 0.0,
                    "isCumulativeCalculation": true,
                    "isPartTimeCalculation": false,
                    "isAutomaticCalculation": false,
                    "isSupplementingDailyWage": false,
                    "minimumMaximumType": {
                        "key": 2
                    }
                }            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.noParentIdNonInheritanceGuid,
                        mode: ValidationMode.partial
                    }
                ]
            }
        }
    }
};

testEndpoint(config);
