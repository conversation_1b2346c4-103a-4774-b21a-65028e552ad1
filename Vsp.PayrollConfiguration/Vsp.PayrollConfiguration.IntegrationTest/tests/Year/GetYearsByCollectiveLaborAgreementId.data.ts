export const expectedResponses = {
    QA_Year_GET_CLA: {
        _embedded: [
            {
                "id": "0000093b-07e4-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1",
                    "type": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "year": 2020,
                "payrollPeriodType": {
                    "key": 1,
                    "value": "Maand"
                },
                "standardShift": {
                    "shiftNumber": 1,
                    "fullTimeHoursPerWeek": 40.2,
                    "bonusPercentage": 0.02
                },
                "standardEmployeeProfile": null,
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardShift": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardEmployeeProfile": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                }
            },
            {
                "id": "0000093b-07e5-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1",
                    "type": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "year": 2021,
                "payrollPeriodType": {
                    "key": 1,
                    "value": "Maand"
                },
                "standardShift": {
                    "shiftNumber": 1,
                    "fullTimeHoursPerWeek": 40.21,
                    "bonusPercentage": 0.021
                },
                "standardEmployeeProfile": null,
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardShift": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardEmployeeProfile": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                }
            },
            {
                "id": "0000093b-07e6-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1",
                    "type": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "year": 2022,
                "payrollPeriodType": {
                    "key": 1,
                    "value": "Maand"
                },
                "standardShift": {
                    "shiftNumber": 1,
                    "fullTimeHoursPerWeek": 40.22,
                    "bonusPercentage": 0.022
                },
                "standardEmployeeProfile": null,
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardShift": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardEmployeeProfile": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                }
            },
            {
                "id": "0000093b-07e7-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1",
                    "type": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "year": 2023,
                "payrollPeriodType": {
                    "key": 1,
                    "value": "Maand"
                },
                "standardShift": {
                    "shiftNumber": 1,
                    "fullTimeHoursPerWeek": 40.23,
                    "bonusPercentage": 0.023
                },
                "standardEmployeeProfile": null,
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardShift": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardEmployeeProfile": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                }
            },
            {
                "id": "0000093b-07e8-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1",
                    "type": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "year": 2024,
                "payrollPeriodType": {
                    "key": 1,
                    "value": "Maand"
                },
                "standardShift": {
                    "shiftNumber": 1,
                    "fullTimeHoursPerWeek": 40.24,
                    "bonusPercentage": 0.024
                },
                "standardEmployeeProfile": null,
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardShift": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardEmployeeProfile": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                }
            },
            {
                "id": "0000093b-07e9-0000-0000-000000000000",
                "inheritanceLevel": {
                    "id": "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1",
                    "type": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                },
                "year": 2025,
                "payrollPeriodType": {
                    "key": 1,
                    "value": "Maand"
                },
                "standardShift": {
                    "shiftNumber": 1,
                    "fullTimeHoursPerWeek": 40.25,
                    "bonusPercentage": 0.025
                },
                "standardEmployeeProfile": null,
                "definedAtLevel": {
                    "payrollPeriodType": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardShift": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    },
                    "standardEmployeeProfile": {
                        "key": 1,
                        "value": "CollectiveLaborAgreement"
                    }
                }
            }
        ]
    },
};
