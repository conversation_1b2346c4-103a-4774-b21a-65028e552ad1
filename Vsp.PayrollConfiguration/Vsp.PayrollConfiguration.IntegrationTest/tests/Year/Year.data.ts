export const collectiveLaborAgreementIds = {
    QA_Year_GET_CLA: "6a1d3e98-8b9f-493f-b9b6-2c52655f49d1",
};

export const wageModelIds = {
    QA_WageModel01Month: "11ed2130-6831-48fb-80d5-0293950cd14b",
    QA_Year_GET_WM: "458d0541-8bdb-4d35-8c6e-606297d15db8",
};

export const payrollAdministrationIds = {
    QA_PPOMaand: "dfc94f53-9384-4384-adaf-c7305531f234",
    QA_Year_GET_PA: "d8706816-6153-455a-934d-4e939a2af6c3",
};

export const yearIds = {
    CollectiveLaborAgreement: {
        QA_PayrollConfiguration1_CLA_ForLevel_CLA: {
            year2025: "000008be-07e9-0000-0000-000000000000",
        },
        QA_PayrollConfiguration1_CLA_ForLevel_CLA_4Week: {
            year2025: "00000965-07e9-0000-0000-000000000000",
        },
        QA_PayrollConfiguration1_CLA_ForLevel_CLA_Week: {
            year2025: "00000966-07e9-0000-0000-000000000000",
        },
    },
    WageModel: {
        QA_WageModel01Month: {
            year2021: "0000042d-07e5-0000-0000-000000000000",
        },
        QA_PayrollConfiguration1_WM_ForLevel_WM: {
            "year2025": "000008c3-07e9-0000-0000-000000000000",
        },
    },
    PayrollAdministration: {
        QA_PayrollConfiguration1_PA_ForLevel_PA: {
            year2025: "000008c7-07e9-0000-0000-000000000000",
        },
    },
    QA_Year_GET_CLA: {
        year2025: "0000093b-07e9-0000-0000-000000000000",
    },
    QA_Year_GET_WM: {
        year2025: "0000093c-07e9-0000-0000-000000000000",
    },
    QA_Year_GET_PA: {
        year2025: "0000093d-07e9-0000-0000-000000000000",
    },
    QA_Year_PATCH_WM: {
        year2025: "00000946-07e9-0000-0000-000000000000",
    },
    QA_Year_PATCH_PA: {
        year2025: "00000947-07e9-0000-0000-000000000000",
    },
};
