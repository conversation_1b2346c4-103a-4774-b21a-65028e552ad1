export const expectedResponses = {
    QA_AKPayrollConfiguration1: {
        _embedded: [
            {
                "comment": "<br>",
                "description": "QA_PayrollConfiguration_4Week_CLA",
                "id": "1aaecb1e-f8e4-4854-a314-c863b88215ca"
            },
            {
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!",
                "description": "QA_PayrollConfiguration_CLA_ForLevel_CLA",
                "id": "c59445b9-6c03-4423-b405-94e89f250895"
            },
            {
                "comment": "<br>",
                "description": "QA_PayrollConfiguration_Week_CLA",
                "id": "c0a9de91-390a-4f12-967a-a17dfbc94cc7"
            },
            {
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!",
                "description": "QA_PayrollConfiguration1_CLA_ForLevel_PA",
                "id": "dfdaa0a5-bce8-4ddd-8e1c-42554334c342"
            },
            {
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!",
                "description": "QA_PayrollConfiguration1_CLA_ForLevel_WM",
                "id": "8c87ffab-20e3-4bcd-808c-8eb9f4e8dfdf"
            },
            {
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!",
                "description": "QA_PayrollConfiguration2_CLA_ForLevel_PA",
                "id": "9ad57900-980e-404a-b8a3-14eca3278552"
            },
            {
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!",
                "description": "QA_PayrollConfiguration2_CLA_ForLevel_WM",
                "id": "969d1c9a-ca97-40d1-851d-048177826eac"
            }
        ]
    },
    QA_AKPayrollConfiguration2: {
        _embedded: [
            {
                "id": "c59445b9-6c03-4423-b405-94e89f250895",
                "description": "QA_PayrollConfiguration_CLA_ForLevel_CLA",
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
            },
            {
                "id": "dfdaa0a5-bce8-4ddd-8e1c-42554334c342",
                "description": "QA_PayrollConfiguration1_CLA_ForLevel_PA",
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
            },
            {
                "id": "8c87ffab-20e3-4bcd-808c-8eb9f4e8dfdf",
                "description": "QA_PayrollConfiguration1_CLA_ForLevel_WM",
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
            },
            {
                "id": "9ad57900-980e-404a-b8a3-14eca3278552",
                "description": "QA_PayrollConfiguration2_CLA_ForLevel_PA",
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
            },
            {
                "id": "969d1c9a-ca97-40d1-851d-048177826eac",
                "description": "QA_PayrollConfiguration2_CLA_ForLevel_WM",
                "comment": "Empty CLA for unit and integration tests. DO NOT EDIT!"
            }
        ]
    },
};
