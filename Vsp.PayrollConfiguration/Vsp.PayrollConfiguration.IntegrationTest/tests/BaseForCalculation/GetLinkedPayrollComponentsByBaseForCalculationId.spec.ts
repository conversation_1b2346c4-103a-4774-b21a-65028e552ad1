import { testEndpoint, ITestConfig, ValidationMode } from "@vsp/qa-api-testing/dist";
import * as sharedData from "../Shared.data";
import * as entityData from "./BaseForCalculation.data";
import * as endpointData from "./GetLinkedPayrollComponentsByBaseForCalculationId.data";

export const config: ITestConfig = {
    ...sharedData.baseConfig,
    route: "basesforcalculation/{baseForCalculationId}/linkedpayrollcomponents",
    method: "GET",
    params: {
        baseForCalculationId: entityData.baseForCalculationIds.QA_BaseForCalculation_GET_CLA.year2025.baseForCalculation_1,
    },
    modules: {
        methodNotAllowed: ["DELETE", "PATCH", "POST", "PUT"],
        authorization: true,
        rechtBasis: false,
    },
    tokens: {
        QA_PayrollConfiguration1: sharedData.users.QA_PayrollConfiguration1
    },
    tests: {
        // ---200---
        "GetLinkedPayrollComponentsByBaseForCalculationId should return 200, the correct list of the payroll components linked to the provided base for calculation, for 2025 at collective labor agreement level to provider user QA_PayrollConfiguration1": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: entityData.baseForCalculationIds.QA_BaseForCalculation_GET_CLA.year2025.baseForCalculation_1,
                },
                queryStringParams: {
                    orderBy: "key",
                },
                timeout: 120000,
            },
            response: {
                code: 200,
                validations: [
                    {
                        key: "version",
                        value: sharedData.versions.v20180101,
                        mode: ValidationMode.partial,
                    },
                    {
                        key: "messages",
                        value: 0,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "totalSize",
                        value: endpointData.expectedResponses.QA_BaseForCalculation_GET_CLA.year2025.baseForCalculation_1._embedded.length,
                        mode: ValidationMode.strict,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_BaseForCalculation_GET_CLA.year2025.baseForCalculation_1._embedded.length,
                        mode: ValidationMode.count,
                    },
                    {
                        key: "_embedded",
                        value: endpointData.expectedResponses.QA_BaseForCalculation_GET_CLA.year2025.baseForCalculation_1._embedded,
                        mode: ValidationMode.partial,
                    },
                ],
            }
        },
        // ---403---
        "GetLinkedPayrollComponentsByBaseForCalculationId should return 403 when baseForCalculationId=non-EntityGuid": {
            request: {
                headers: sharedData.requestHeaders,
                token: "QA_PayrollConfiguration1",
                params: {
                    baseForCalculationId: sharedData.nonEntityGuid,
                },
            },
            response: {
                code: 403,
                validations: [
                    {
                        value: sharedData.defaultErrors.unknownInheritanceLevel,
                        mode: ValidationMode.partial
                    }
                ]
            }
        },
    }
};

describe(config.route + " " + config.method, () => {
    testEndpoint(config);
});