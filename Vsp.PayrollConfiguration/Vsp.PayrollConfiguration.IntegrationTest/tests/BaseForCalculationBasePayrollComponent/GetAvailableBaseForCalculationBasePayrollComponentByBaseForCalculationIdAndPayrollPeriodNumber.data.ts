export const expectedResponses = {
    QA_BFC_BasePayrollComponent_GET_CLA: {
        year2025: {
            baseForCalculation_1: {
                _embedded: [
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN GEWERKT",
                        "key": 1
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN VAKANTIE",
                        "key": 2
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN BET.VERZ.",
                        "key": 3
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN ONB.VERZ.",
                        "key": 4
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN REIS BINN",
                        "key": 20
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN REIS BUIT",
                        "key": 21
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN VV",
                        "key": 23
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN WWVV",
                        "key": 24
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN WAO/WIA",
                        "key": 25
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN WW",
                        "key": 26
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN NAB.VERLF",
                        "key": 27
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "MEERUREN",
                        "key": 28
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "ATV UREN",
                        "key": 30
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "OPGENOMEN TVT",
                        "key": 31
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN VERZ. CAO",
                        "key": 32
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN FEESTDGN.",
                        "key": 33
                    },
                    {
                        "category": {
                            "key": 2,
                            "value": "Ziekte vangnet"
                        },
                        "description": "UREN ZIEK VNET",
                        "key": 34
                    },
                    {
                        "category": {
                            "key": 2,
                            "value": "Ziekte vangnet"
                        },
                        "description": "WACHTUREN VNET",
                        "key": 35
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN ZIEK",
                        "key": 37
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "WACHTUREN ZIEK",
                        "key": 38
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UREN NSUP ZIEK",
                        "key": 39
                    },
                    {
                        "category": {
                            "key": 6,
                            "value": "Eenheden"
                        },
                        "description": "KM.ONBELAST",
                        "key": 41
                    },
                    {
                        "category": {
                            "key": 6,
                            "value": "Eenheden"
                        },
                        "description": "KM.BELAST",
                        "key": 42
                    },
                    {
                        "category": {
                            "key": 98,
                            "value": "Intern"
                        },
                        "description": "FRANCH.WW-AWF",
                        "key": 64
                    },
                    {
                        "category": {
                            "key": 98,
                            "value": "Intern"
                        },
                        "description": "PRLN SECTORFDS",
                        "key": 65
                    },
                    {
                        "category": {
                            "key": 98,
                            "value": "Intern"
                        },
                        "description": "ARBEIDSKORT.",
                        "key": 66
                    },
                    {
                        "category": {
                            "key": 1,
                            "value": "Uren"
                        },
                        "description": "UITB.VAK.UREN",
                        "key": 67
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "VAKANTIEUREN",
                        "key": 68
                    },
                    {
                        "category": {
                            "key": 98,
                            "value": "Intern"
                        },
                        "description": "AFDR.WAO(VERV)",
                        "key": 69
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "LOON/SALARIS",
                        "key": 70
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "VAKANTIELOON",
                        "key": 71
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "BET.VERZUIM",
                        "key": 72
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "OVERWERKLOON",
                        "key": 73
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "OVERW.TOESLAG",
                        "key": 74
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "PLOEGENTOESLAG",
                        "key": 75
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "ONREGELM.TOESL",
                        "key": 76
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "REISGLD BINNEN",
                        "key": 79
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "REISGLD BUITEN",
                        "key": 80
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "SPECIALISTBEZ.",
                        "key": 81
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "VAKANTIE TOESL",
                        "key": 82
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "EINDEJRS UITK",
                        "key": 83
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "TANTIEME",
                        "key": 84
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "13E MAAND",
                        "key": 85
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "NABETALING",
                        "key": 86
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "LOON ATV-UREN",
                        "key": 87
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "LOON TVT-UREN",
                        "key": 88
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "BET.VERZ. CAO",
                        "key": 89
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "UITK. VV",
                        "key": 90
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "UITK. WWVV",
                        "key": 91
                    },
                    {
                        "category": {
                            "key": 26,
                            "value": "Output"
                        },
                        "description": "UITKERING VUT",
                        "key": 92
                    }
                ]
            },
        }
    }
};